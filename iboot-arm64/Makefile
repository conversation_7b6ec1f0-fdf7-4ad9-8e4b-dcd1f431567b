# iBooy Bootloader Makefile

# Toolchain Configuration
CROSS_COMPILE ?= aarch64-linux-gnu-
CC = $(CROSS_COMPILE)gcc
AS = $(CROSS_COMPILE)as
LD = $(CROSS_COMPILE)ld
OBJCOPY = $(CROSS_COMPILE)objcopy
OBJDUMP = $(CROSS_COMPILE)objdump

# Build Configuration
TARGET = ibooy
BUILD_DIR = build
ARCH = arm64

# Compiler Flags
CFLAGS = -Wall -Wextra -Werror -std=c99 -O2 -g
CFLAGS += -ffreestanding -nostdlib -nostartfiles
CFLAGS += -mcpu=cortex-a57 -march=armv8-a
CFLAGS += -mgeneral-regs-only -fno-stack-protector
CFLAGS += -fno-common -fno-builtin -fno-strict-aliasing
CFLAGS += -Iinclude

# Assembler Flags
ASFLAGS = -mcpu=cortex-a57 -march=armv8-a

# Linker Flags
LDFLAGS = -nostdlib -nostartfiles -static
LDFLAGS += -T link.ld

# Source Files
ARCH_SOURCES = arch/arm64/start.S \
               arch/arm64/mmu.c \
               arch/arm64/cpu.c \
               arch/arm64/exceptions.c

BOOT_SOURCES = boot/main.c \
               boot/boot.c \
               boot/loader.c \
               boot/crypto.c \
               boot/pac.c

FS_SOURCES = fs/fat.c \
             fs/apfs.c \
             fs/img4.c \
             fs/lzss.c

DRIVER_SOURCES = drivers/uart.c \
                 drivers/timer.c \
                 drivers/gui.c \
                 drivers/shell.c

DTB_SOURCES = dtb/dtb_loader.c \
              dtb/fdt.c \
              dtb/smbios.c

PLATFORM_SOURCES = platform/qemu_arm64.c \
                   platform/m1_fake.c \
                   platform/rpi4.c

PATCH_SOURCES = patches/patches.c \
                patches/secureboot.c \
                patches/t8103.c

ALL_SOURCES = $(ARCH_SOURCES) $(BOOT_SOURCES) $(FS_SOURCES) \
              $(DRIVER_SOURCES) $(DTB_SOURCES) $(PLATFORM_SOURCES) \
              $(PATCH_SOURCES)

# Object Files
OBJECTS = $(patsubst %.c,$(BUILD_DIR)/%.o,$(filter %.c,$(ALL_SOURCES)))
OBJECTS += $(patsubst %.S,$(BUILD_DIR)/%.o,$(filter %.S,$(ALL_SOURCES)))

# Default Target
all: $(BUILD_DIR)/$(TARGET).bin

# Create Build Directory
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)
	mkdir -p $(BUILD_DIR)/arch/arm64
	mkdir -p $(BUILD_DIR)/boot
	mkdir -p $(BUILD_DIR)/fs
	mkdir -p $(BUILD_DIR)/drivers
	mkdir -p $(BUILD_DIR)/dtb
	mkdir -p $(BUILD_DIR)/platform
	mkdir -p $(BUILD_DIR)/patches

# Compile C Sources
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

# Compile Assembly Sources
$(BUILD_DIR)/%.o: %.S | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(ASFLAGS) -c $< -o $@

# Link Bootloader
$(BUILD_DIR)/$(TARGET).elf: $(OBJECTS) link.ld
	$(LD) $(LDFLAGS) $(OBJECTS) -o $@

# Create Binary
$(BUILD_DIR)/$(TARGET).bin: $(BUILD_DIR)/$(TARGET).elf
	$(OBJCOPY) -O binary $< $@

# Create Disassembly
$(BUILD_DIR)/$(TARGET).dis: $(BUILD_DIR)/$(TARGET).elf
	$(OBJDUMP) -D $< > $@

# QEMU Testing
qemu: $(BUILD_DIR)/$(TARGET).bin
	qemu-system-aarch64 \
		-M virt \
		-cpu cortex-a57 \
		-m 128M \
		-nographic \
		-kernel $(BUILD_DIR)/$(TARGET).bin \
		-serial stdio

# QEMU with GDB
qemu-gdb: $(BUILD_DIR)/$(TARGET).bin
	qemu-system-aarch64 \
		-M virt \
		-cpu cortex-a57 \
		-m 128M \
		-nographic \
		-kernel $(BUILD_DIR)/$(TARGET).bin \
		-serial stdio \
		-s -S

# Debug with GDB
gdb:
	$(CROSS_COMPILE)gdb $(BUILD_DIR)/$(TARGET).elf \
		-ex "target remote localhost:1234" \
		-ex "layout src"

# Clean Build
clean:
	rm -rf $(BUILD_DIR)

# Install Dependencies (Ubuntu/Debian)
deps:
	sudo apt-get update
	sudo apt-get install -y gcc-aarch64-linux-gnu qemu-system-arm

# Show Build Info
info:
	@echo "Target: $(TARGET)"
	@echo "Architecture: $(ARCH)"
	@echo "Cross Compiler: $(CROSS_COMPILE)gcc"
	@echo "Build Directory: $(BUILD_DIR)"
	@echo "Sources: $(words $(ALL_SOURCES)) files"

# Help
help:
	@echo "iBooy Bootloader Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all       - Build bootloader binary"
	@echo "  clean     - Clean build directory"
	@echo "  qemu      - Run in QEMU"
	@echo "  qemu-gdb  - Run in QEMU with GDB server"
	@echo "  gdb       - Connect GDB to QEMU"
	@echo "  deps      - Install build dependencies"
	@echo "  info      - Show build information"
	@echo "  help      - Show this help"

.PHONY: all clean qemu qemu-gdb gdb deps info help
