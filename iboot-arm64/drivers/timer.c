/*
 * iBooy Bootloader - Timer Driver
 */

#include "../include/config.h"
#include <stdint.h>

static uint64_t timer_freq = TIMER_FREQUENCY;

void timer_init(void)
{
    /* ARM Generic Timer is already running */
    printf("Timer initialized (frequency: %lu Hz)\n", timer_freq);
}

uint64_t timer_get_ticks(void)
{
    uint64_t ticks;
    __asm__ volatile("mrs %0, cntvct_el0" : "=r" (ticks));
    return ticks;
}

void timer_delay_ms(uint32_t ms)
{
    uint64_t start = timer_get_ticks();
    uint64_t delay_ticks = (timer_freq * ms) / 1000;
    while ((timer_get_ticks() - start) < delay_ticks) {
        __asm__ volatile("nop");
    }
}
