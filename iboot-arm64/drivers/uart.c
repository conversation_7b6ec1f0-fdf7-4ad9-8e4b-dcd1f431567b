/*
 * iBooy Bootloader - UART Driver
 * 
 * This file implements UART driver for serial communication.
 */

#include "../include/config.h"

/* UART registers (PL011) */
#define UART_DR     0x000   /* Data Register */
#define UART_RSR    0x004   /* Receive Status Register */
#define UART_FR     0x018   /* Flag Register */
#define UART_ILPR   0x020   /* IrDA Low-Power Counter Register */
#define UART_IBRD   0x024   /* Integer Baud Rate Divisor */
#define UART_FBRD   0x028   /* Fractional Baud Rate Divisor */
#define UART_LCR_H  0x02C   /* Line Control Register */
#define UART_CR     0x030   /* Control Register */
#define UART_IFLS   0x034   /* Interrupt FIFO Level Select Register */
#define UART_IMSC   0x038   /* Interrupt Mask Set/Clear Register */
#define UART_RIS    0x03C   /* Raw Interrupt Status Register */
#define UART_MIS    0x040   /* Masked Interrupt Status Register */
#define UART_ICR    0x044   /* Interrupt Clear Register */

/* Flag Register bits */
#define UART_FR_TXFE    (1 << 7)    /* Transmit FIFO empty */
#define UART_FR_RXFF    (1 << 6)    /* Receive FIFO full */
#define UART_FR_TXFF    (1 << 5)    /* Transmit FIFO full */
#define UART_FR_RXFE    (1 << 4)    /* Receive FIFO empty */
#define UART_FR_BUSY    (1 << 3)    /* UART busy */

/* Line Control Register bits */
#define UART_LCR_H_FEN  (1 << 4)    /* Enable FIFOs */
#define UART_LCR_H_WLEN_8   (3 << 5)   /* 8-bit word length */

/* Control Register bits */
#define UART_CR_UARTEN  (1 << 0)    /* UART enable */
#define UART_CR_TXE     (1 << 8)    /* Transmit enable */
#define UART_CR_RXE     (1 << 9)    /* Receive enable */

/* UART context */
struct uart_context {
    uint32_t initialized;
    uint64_t base_addr;
    uint32_t baudrate;
    uint32_t clock_freq;
};

static struct uart_context uart_ctx;

/*
 * Read UART register
 */
static uint32_t uart_read_reg(uint32_t offset)
{
    return REG32(uart_ctx.base_addr + offset);
}

/*
 * Write UART register
 */
static void uart_write_reg(uint32_t offset, uint32_t value)
{
    REG32(uart_ctx.base_addr + offset) = value;
}

/*
 * Initialize UART
 */
void uart_init(void)
{
    uart_ctx.base_addr = UART_BASE;
    uart_ctx.baudrate = UART_BAUDRATE;
    uart_ctx.clock_freq = UART_CLOCK;
    
    /* Disable UART */
    uart_write_reg(UART_CR, 0);
    
    /* Clear all interrupts */
    uart_write_reg(UART_ICR, 0x7FF);
    
    /* Set baud rate */
    uint32_t divisor = (uart_ctx.clock_freq * 4) / uart_ctx.baudrate;
    uart_write_reg(UART_IBRD, divisor >> 6);
    uart_write_reg(UART_FBRD, divisor & 0x3F);
    
    /* Set line control: 8N1, enable FIFOs */
    uart_write_reg(UART_LCR_H, UART_LCR_H_WLEN_8 | UART_LCR_H_FEN);
    
    /* Enable UART, TX, and RX */
    uart_write_reg(UART_CR, UART_CR_UARTEN | UART_CR_TXE | UART_CR_RXE);
    
    uart_ctx.initialized = 1;
}

/*
 * Send a character
 */
void uart_putc(char c)
{
    if (!uart_ctx.initialized) {
        return;
    }
    
    /* Wait for TX FIFO to have space */
    while (uart_read_reg(UART_FR) & UART_FR_TXFF) {
        /* Wait */
    }
    
    /* Send character */
    uart_write_reg(UART_DR, c);
    
    /* Convert LF to CRLF */
    if (c == '\n') {
        while (uart_read_reg(UART_FR) & UART_FR_TXFF) {
            /* Wait */
        }
        uart_write_reg(UART_DR, '\r');
    }
}

/*
 * Send a string
 */
void uart_puts(const char *str)
{
    if (!str) {
        return;
    }
    
    while (*str) {
        uart_putc(*str++);
    }
}

/*
 * Receive a character (non-blocking)
 */
int uart_getc(void)
{
    if (!uart_ctx.initialized) {
        return -1;
    }
    
    /* Check if RX FIFO has data */
    if (uart_read_reg(UART_FR) & UART_FR_RXFE) {
        return -1;  /* No data available */
    }
    
    /* Read character */
    return uart_read_reg(UART_DR) & 0xFF;
}

/*
 * Receive a character (blocking)
 */
char uart_getc_blocking(void)
{
    int c;
    
    do {
        c = uart_getc();
    } while (c < 0);
    
    return (char)c;
}

/*
 * Check if data is available
 */
int uart_data_available(void)
{
    if (!uart_ctx.initialized) {
        return 0;
    }
    
    return !(uart_read_reg(UART_FR) & UART_FR_RXFE);
}

/*
 * Flush TX FIFO
 */
void uart_flush(void)
{
    if (!uart_ctx.initialized) {
        return;
    }
    
    /* Wait for TX FIFO to empty and UART to not be busy */
    while (!(uart_read_reg(UART_FR) & UART_FR_TXFE) || 
           (uart_read_reg(UART_FR) & UART_FR_BUSY)) {
        /* Wait */
    }
}

/*
 * Read a line from UART
 */
int uart_gets(char *buffer, size_t size)
{
    size_t pos = 0;
    char c;
    
    if (!buffer || size == 0) {
        return -1;
    }
    
    while (pos < size - 1) {
        c = uart_getc_blocking();
        
        if (c == '\r' || c == '\n') {
            break;
        }
        
        if (c == '\b' || c == 0x7F) {  /* Backspace or DEL */
            if (pos > 0) {
                pos--;
                uart_puts("\b \b");  /* Erase character on screen */
            }
            continue;
        }
        
        if (c >= 32 && c <= 126) {  /* Printable characters */
            buffer[pos++] = c;
            uart_putc(c);  /* Echo character */
        }
    }
    
    buffer[pos] = '\0';
    uart_puts("\n");
    
    return pos;
}

/*
 * Set baud rate
 */
int uart_set_baudrate(uint32_t baudrate)
{
    if (!uart_ctx.initialized) {
        return IBOOY_ERROR_INVALID;
    }
    
    /* Disable UART */
    uint32_t cr = uart_read_reg(UART_CR);
    uart_write_reg(UART_CR, cr & ~UART_CR_UARTEN);
    
    /* Set new baud rate */
    uint32_t divisor = (uart_ctx.clock_freq * 4) / baudrate;
    uart_write_reg(UART_IBRD, divisor >> 6);
    uart_write_reg(UART_FBRD, divisor & 0x3F);
    
    /* Re-enable UART */
    uart_write_reg(UART_CR, cr);
    
    uart_ctx.baudrate = baudrate;
    
    return IBOOY_SUCCESS;
}

/*
 * Get UART status
 */
uint32_t uart_get_status(void)
{
    if (!uart_ctx.initialized) {
        return 0;
    }
    
    return uart_read_reg(UART_FR);
}

/*
 * Check if UART is initialized
 */
int uart_is_initialized(void)
{
    return uart_ctx.initialized;
}
