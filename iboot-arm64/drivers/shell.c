/*
 * iBooy Bootloader - Interactive Shell
 */

#include "../include/config.h"
#include <stdint.h>

extern int uart_gets(char *buffer, size_t size);
extern void uart_puts(const char *str);

static char cmd_buffer[SHELL_MAX_CMDLINE];

void shell_init(void)
{
    if (!SHELL_ENABLED) return;
    printf("Shell initialized\n");
}

static void shell_help(void)
{
    printf("Available commands:\n");
    printf("  help    - Show this help\n");
    printf("  boot    - Boot kernel\n");
    printf("  reset   - Reset system\n");
    printf("  info    - Show system info\n");
}

void shell_run(void)
{
    if (!SHELL_ENABLED) return;
    
    printf("\nEntering interactive shell\n");
    printf("Type 'help' for available commands\n");
    
    while (1) {
        printf(SHELL_PROMPT);
        
        if (uart_gets(cmd_buffer, sizeof(cmd_buffer)) <= 0) {
            continue;
        }
        
        if (strcmp(cmd_buffer, "help") == 0) {
            shell_help();
        } else if (strcmp(cmd_buffer, "boot") == 0) {
            printf("Attempting to boot kernel...\n");
            extern void boot_kernel(void);
            boot_kernel();
        } else if (strcmp(cmd_buffer, "reset") == 0) {
            printf("Resetting system...\n");
            while(1) __asm__ volatile("wfi");
        } else if (strcmp(cmd_buffer, "info") == 0) {
            extern void cpu_print_info(void);
            cpu_print_info();
        } else if (strlen(cmd_buffer) > 0) {
            printf("Unknown command: %s\n", cmd_buffer);
        }
    }
}
