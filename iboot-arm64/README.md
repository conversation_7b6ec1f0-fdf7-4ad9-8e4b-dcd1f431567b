# iBooy - Custom ARM64 Bootloader

A custom iBoot-style bootloader for ARM64 platforms, designed to work with QEMU and other ARM64 systems.

## Features

- **ARM64 Architecture Support**: Full AArch64 support with MMU, exception handling, and CPU initialization
- **Multiple Platform Support**: QEMU virt machine, M1 fake platform, and Raspberry Pi 4
- **Filesystem Support**: FAT, APFS, IMG4, and LZSS compression
- **Security Features**: Secure boot, pointer authentication, and cryptographic functions
- **Device Drivers**: UART, timer, GUI, and interactive shell
- **Device Tree Support**: DTB loading, FDT parsing, and SMBIOS
- **Runtime Patching**: Dynamic patching system for platform-specific modifications

## Directory Structure

```
iboot-arm64/
├── arch/arm64/          # Architecture-specific code
│   ├── start.S          # Boot entry point
│   ├── mmu.c           # Memory management unit
│   ├── cpu.c           # CPU initialization
│   └── exceptions.c    # Exception handling
├── boot/               # Boot subsystem
│   ├── main.c          # Main entry point
│   ├── boot.c          # Boot logic
│   ├── loader.c        # Kernel loading
│   ├── crypto.c        # Cryptographic functions
│   └── pac.c           # Pointer authentication
├── fs/                 # Filesystem support
│   ├── fat.c           # FAT filesystem
│   ├── apfs.c          # APFS support
│   ├── img4.c          # IMG4 format
│   └── lzss.c          # LZSS compression
├── drivers/            # Device drivers
│   ├── uart.c          # Serial communication
│   ├── timer.c         # Timer support
│   ├── gui.c           # Basic GUI
│   └── shell.c         # Command shell
├── dtb/                # Device tree support
│   ├── dtb_loader.c    # DTB loading
│   ├── fdt.c           # FDT parsing
│   └── smbios.c        # SMBIOS support
├── platform/           # Platform-specific code
│   ├── qemu_arm64.c    # QEMU virt machine
│   ├── m1_fake.c       # M1 fake platform
│   └── rpi4.c          # Raspberry Pi 4
├── patches/            # Runtime patching
│   ├── patches.c       # Patch system
│   ├── secureboot.c    # Secure boot
│   └── t8103.c         # M1 chip support
├── include/            # Header files
│   └── config.h        # Configuration
├── Makefile            # Build system
├── link.ld             # Linker script
└── README.md           # This file
```

## Building

### Prerequisites

Install the ARM64 cross-compilation toolchain:

```bash
# Ubuntu/Debian
sudo apt-get install gcc-aarch64-linux-gnu qemu-system-arm

# Or use the provided target
make deps
```

### Build Commands

```bash
# Build the bootloader
make

# Clean build files
make clean

# Show build information
make info

# Show help
make help
```

## Running

### QEMU

Run the bootloader in QEMU:

```bash
# Normal execution
make qemu

# With GDB debugging
make qemu-gdb

# In another terminal, connect GDB
make gdb
```

### Manual QEMU Command

```bash
qemu-system-aarch64 \
    -M virt \
    -cpu cortex-a57 \
    -m 128M \
    -nographic \
    -kernel build/ibooy.bin \
    -serial stdio
```

## Configuration

Edit `include/config.h` to customize:

- Memory layout and sizes
- Platform selection
- Feature enablement
- Debug options
- Security settings

## Memory Layout

- **Base Address**: 0x40000000 (1GB)
- **Bootloader**: 0x40000000 - 0x40100000 (1MB)
- **Stack**: 64KB
- **Heap**: 1MB
- **Kernel Load**: 0x41000000 (32MB max)
- **DTB Load**: 0x43000000 (64KB max)
- **Ramdisk Load**: 0x44000000

## Boot Process

1. **Hardware Initialization**: CPU, MMU, exception vectors
2. **Platform Detection**: Identify target platform
3. **Driver Initialization**: UART, timer, storage
4. **Filesystem Mount**: Detect and mount boot filesystem
5. **Kernel Loading**: Load kernel, DTB, and ramdisk
6. **Security Validation**: Verify signatures and checksums
7. **Kernel Handoff**: Transfer control to loaded kernel

## Security Features

- **Secure Boot**: Cryptographic verification of boot chain
- **Pointer Authentication**: ARM64 pointer authentication support
- **Memory Protection**: MMU-based memory isolation
- **Runtime Patching**: Secure runtime modification system

## Debugging

The bootloader includes extensive debugging support:

- Serial console output
- Interactive shell
- GDB debugging support
- Memory dump utilities
- Boot trace logging

## Platform Support

### QEMU (Default)
- Full feature support
- Ideal for development and testing

### M1 Fake Platform
- Simulated M1 environment
- T8103 chip emulation

### Raspberry Pi 4
- Real hardware support
- GPIO and peripheral access

## Contributing

1. Follow the existing code style
2. Add appropriate error handling
3. Include debug output for new features
4. Test on QEMU before submitting
5. Update documentation as needed

## License

This project is for educational and research purposes. Please respect Apple's intellectual property and use responsibly.

## Disclaimer

This is a custom implementation inspired by iBoot but is not affiliated with or endorsed by Apple Inc. It is intended for educational purposes and legitimate security research only.
