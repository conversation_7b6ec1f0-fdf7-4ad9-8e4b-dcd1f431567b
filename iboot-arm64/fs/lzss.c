/*
 * iBooy Bootloader - LZSS Compression Support
 * 
 * This file implements LZSS decompression for compressed kernels.
 */

#include "../include/config.h"
#include <stdint.h>
#include <stddef.h>

/* LZSS parameters */
#define LZSS_N          4096    /* Size of ring buffer */
#define LZSS_F          18      /* Upper limit for match length */
#define LZSS_THRESHOLD  2       /* Encode string into position and length if match length is greater than this */

/* LZSS context */
struct lzss_context {
    uint8_t text_buf[LZSS_N + LZSS_F - 1];
    const uint8_t *src;
    size_t src_pos;
    size_t src_len;
    uint8_t *dst;
    size_t dst_pos;
    size_t dst_len;
    uint32_t flags;
    uint32_t flag_count;
};

/*
 * Get next byte from source
 */
static int get_byte(struct lzss_context *ctx)
{
    if (ctx->src_pos >= ctx->src_len) {
        return -1;
    }
    return ctx->src[ctx->src_pos++];
}

/*
 * Put byte to destination
 */
static int put_byte(struct lzss_context *ctx, uint8_t byte)
{
    if (ctx->dst_pos >= ctx->dst_len) {
        return -1;
    }
    ctx->dst[ctx->dst_pos++] = byte;
    return 0;
}

/*
 * Get next flag bit
 */
static int get_flag_bit(struct lzss_context *ctx)
{
    if (ctx->flag_count == 0) {
        int flags = get_byte(ctx);
        if (flags < 0) {
            return -1;
        }
        ctx->flags = flags;
        ctx->flag_count = 8;
    }
    
    int bit = (ctx->flags & 1);
    ctx->flags >>= 1;
    ctx->flag_count--;
    
    return bit;
}

/*
 * LZSS decompression
 */
int lzss_decompress(const void *src, size_t src_len, void *dst, size_t dst_len)
{
    struct lzss_context ctx;
    int i, j, k, r, c;
    
    if (!src || !dst || src_len == 0 || dst_len == 0) {
        return IBOOY_ERROR_INVALID;
    }
    
    printf("LZSS decompressing: %zu -> %zu bytes\n", src_len, dst_len);
    
    /* Initialize context */
    memset(&ctx, 0, sizeof(ctx));
    ctx.src = (const uint8_t *)src;
    ctx.src_len = src_len;
    ctx.dst = (uint8_t *)dst;
    ctx.dst_len = dst_len;
    
    /* Initialize text buffer */
    memset(ctx.text_buf, ' ', LZSS_N - LZSS_F);
    r = LZSS_N - LZSS_F;
    
    /* Main decompression loop */
    while (ctx.src_pos < ctx.src_len && ctx.dst_pos < ctx.dst_len) {
        int flag = get_flag_bit(&ctx);
        if (flag < 0) {
            break;
        }
        
        if (flag) {
            /* Literal byte */
            c = get_byte(&ctx);
            if (c < 0) {
                break;
            }
            
            if (put_byte(&ctx, c) < 0) {
                break;
            }
            
            ctx.text_buf[r++] = c;
            r &= (LZSS_N - 1);
        } else {
            /* Length-distance pair */
            i = get_byte(&ctx);
            if (i < 0) {
                break;
            }
            
            j = get_byte(&ctx);
            if (j < 0) {
                break;
            }
            
            i |= ((j & 0xf0) << 4);
            j = (j & 0x0f) + LZSS_THRESHOLD;
            
            /* Copy from text buffer */
            for (k = 0; k <= j; k++) {
                c = ctx.text_buf[(i + k) & (LZSS_N - 1)];
                
                if (put_byte(&ctx, c) < 0) {
                    goto done;
                }
                
                ctx.text_buf[r++] = c;
                r &= (LZSS_N - 1);
            }
        }
    }
    
done:
    printf("LZSS decompression completed: %zu bytes output\n", ctx.dst_pos);
    
    if (ctx.dst_pos == 0) {
        return IBOOY_ERROR_INVALID;
    }
    
    return IBOOY_SUCCESS;
}

/*
 * Check if data is LZSS compressed
 */
int lzss_is_compressed(const void *data, size_t size)
{
    /* Simple heuristic: check for common LZSS patterns */
    if (size < 16) {
        return 0;
    }
    
    const uint8_t *bytes = (const uint8_t *)data;
    
    /* Look for flag bytes followed by literal/reference patterns */
    int literal_count = 0;
    int reference_count = 0;
    
    for (size_t i = 0; i < MIN(size, 256); i += 9) {
        if (i + 8 >= size) break;
        
        uint8_t flags = bytes[i];
        
        for (int bit = 0; bit < 8 && i + 1 + bit < size; bit++) {
            if (flags & (1 << bit)) {
                literal_count++;
            } else {
                reference_count++;
                if (i + 2 + bit < size) {
                    bit++; /* Skip second byte of reference */
                }
            }
        }
    }
    
    /* If we have a reasonable mix of literals and references, it might be LZSS */
    return (literal_count > 0 && reference_count > 0 && 
            reference_count < literal_count * 2);
}

/*
 * Get decompressed size estimate
 */
size_t lzss_get_decompressed_size_estimate(const void *data, size_t compressed_size)
{
    /* Conservative estimate: assume 2:1 compression ratio */
    return compressed_size * 2;
}

/*
 * Decompress in-place (if possible)
 */
int lzss_decompress_inplace(void *data, size_t compressed_size, size_t buffer_size, size_t *decompressed_size)
{
    /* For in-place decompression, we need to work backwards or use a temporary buffer */
    /* This is a simplified implementation that uses a temporary buffer */
    
    static uint8_t temp_buffer[1024 * 1024];  /* 1MB temp buffer */
    
    if (compressed_size > sizeof(temp_buffer)) {
        printf("ERROR: Compressed data too large for temp buffer\n");
        return IBOOY_ERROR_INVALID;
    }
    
    /* Copy compressed data to temp buffer */
    memcpy(temp_buffer, data, compressed_size);
    
    /* Decompress from temp buffer to original location */
    int ret = lzss_decompress(temp_buffer, compressed_size, data, buffer_size);
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    
    /* Calculate actual decompressed size */
    *decompressed_size = buffer_size;  /* This should be calculated properly */
    
    return IBOOY_SUCCESS;
}

/*
 * Simple compression (for testing)
 */
int lzss_compress(const void *src, size_t src_len, void *dst, size_t dst_len, size_t *compressed_size)
{
    /* Simple implementation: just copy data with literal flags */
    if (dst_len < src_len + (src_len / 8) + 1) {
        return IBOOY_ERROR_INVALID;
    }
    
    const uint8_t *src_bytes = (const uint8_t *)src;
    uint8_t *dst_bytes = (uint8_t *)dst;
    size_t dst_pos = 0;
    size_t src_pos = 0;
    
    while (src_pos < src_len) {
        /* Write flag byte (all literals) */
        dst_bytes[dst_pos++] = 0xFF;
        
        /* Write up to 8 literal bytes */
        for (int i = 0; i < 8 && src_pos < src_len; i++) {
            dst_bytes[dst_pos++] = src_bytes[src_pos++];
        }
    }
    
    *compressed_size = dst_pos;
    return IBOOY_SUCCESS;
}
