/*
 * iBooy Bootloader - FAT Filesystem Support
 * 
 * This file implements basic FAT filesystem support for loading files.
 */

#include "../include/config.h"
#include <stdint.h>
#include <stddef.h>

/* FAT Boot Sector structure */
struct fat_boot_sector {
    uint8_t jump[3];                /* Jump instruction */
    uint8_t oem_name[8];           /* OEM name */
    uint16_t bytes_per_sector;     /* Bytes per sector */
    uint8_t sectors_per_cluster;   /* Sectors per cluster */
    uint16_t reserved_sectors;     /* Reserved sectors */
    uint8_t num_fats;              /* Number of FATs */
    uint16_t root_entries;         /* Root directory entries */
    uint16_t total_sectors_16;     /* Total sectors (16-bit) */
    uint8_t media_type;            /* Media type */
    uint16_t sectors_per_fat_16;   /* Sectors per FAT (16-bit) */
    uint16_t sectors_per_track;    /* Sectors per track */
    uint16_t num_heads;            /* Number of heads */
    uint32_t hidden_sectors;       /* Hidden sectors */
    uint32_t total_sectors_32;     /* Total sectors (32-bit) */
    
    /* FAT32 specific */
    uint32_t sectors_per_fat_32;   /* Sectors per FAT (32-bit) */
    uint16_t flags;                /* Flags */
    uint16_t version;              /* Version */
    uint32_t root_cluster;         /* Root directory cluster */
    uint16_t fsinfo_sector;        /* FSInfo sector */
    uint16_t backup_boot_sector;   /* Backup boot sector */
    uint8_t reserved[12];          /* Reserved */
    uint8_t drive_number;          /* Drive number */
    uint8_t reserved1;             /* Reserved */
    uint8_t boot_signature;        /* Boot signature */
    uint32_t volume_id;            /* Volume ID */
    uint8_t volume_label[11];      /* Volume label */
    uint8_t fs_type[8];            /* Filesystem type */
} __attribute__((packed));

/* FAT Directory Entry */
struct fat_dir_entry {
    uint8_t name[11];              /* Filename (8.3 format) */
    uint8_t attr;                  /* Attributes */
    uint8_t reserved;              /* Reserved */
    uint8_t create_time_tenth;     /* Creation time (tenths) */
    uint16_t create_time;          /* Creation time */
    uint16_t create_date;          /* Creation date */
    uint16_t access_date;          /* Last access date */
    uint16_t cluster_high;         /* High 16 bits of cluster */
    uint16_t modify_time;          /* Modification time */
    uint16_t modify_date;          /* Modification date */
    uint16_t cluster_low;          /* Low 16 bits of cluster */
    uint32_t size;                 /* File size */
} __attribute__((packed));

/* FAT attributes */
#define FAT_ATTR_READ_ONLY  0x01
#define FAT_ATTR_HIDDEN     0x02
#define FAT_ATTR_SYSTEM     0x04
#define FAT_ATTR_VOLUME_ID  0x08
#define FAT_ATTR_DIRECTORY  0x10
#define FAT_ATTR_ARCHIVE    0x20
#define FAT_ATTR_LFN        0x0F

/* FAT context */
struct fat_context {
    uint32_t mounted;
    uint32_t fat_type;             /* 12, 16, or 32 */
    uint32_t bytes_per_sector;
    uint32_t sectors_per_cluster;
    uint32_t reserved_sectors;
    uint32_t num_fats;
    uint32_t sectors_per_fat;
    uint32_t root_cluster;
    uint32_t data_start_sector;
    uint32_t fat_start_sector;
    uint8_t *sector_buffer;
};

static struct fat_context fat_ctx;
static uint8_t sector_buffer[512];

/* Forward declarations */
extern int storage_read_sector(uint32_t sector, void *buffer);

/*
 * Read a sector from storage
 */
static int read_sector(uint32_t sector, void *buffer)
{
    /* TODO: Implement actual storage reading */
    /* For simulation, just clear the buffer */
    memset(buffer, 0, 512);
    
    /* Simulate a valid FAT32 boot sector for sector 0 */
    if (sector == 0) {
        struct fat_boot_sector *bs = (struct fat_boot_sector *)buffer;
        bs->jump[0] = 0xEB;
        bs->jump[1] = 0x58;
        bs->jump[2] = 0x90;
        memcpy(bs->oem_name, "IBOOY   ", 8);
        bs->bytes_per_sector = 512;
        bs->sectors_per_cluster = 8;
        bs->reserved_sectors = 32;
        bs->num_fats = 2;
        bs->sectors_per_fat_32 = 1024;
        bs->root_cluster = 2;
        bs->boot_signature = 0x29;
        memcpy(bs->fs_type, "FAT32   ", 8);
        
        /* Boot signature */
        ((uint8_t *)buffer)[510] = 0x55;
        ((uint8_t *)buffer)[511] = 0xAA;
    }
    
    return IBOOY_SUCCESS;
}

/*
 * Determine FAT type
 */
static uint32_t determine_fat_type(const struct fat_boot_sector *bs)
{
    uint32_t total_sectors;
    uint32_t data_sectors;
    uint32_t cluster_count;
    
    /* Calculate total sectors */
    if (bs->total_sectors_16 != 0) {
        total_sectors = bs->total_sectors_16;
    } else {
        total_sectors = bs->total_sectors_32;
    }
    
    /* Calculate data sectors */
    uint32_t sectors_per_fat = (bs->sectors_per_fat_16 != 0) ? 
                               bs->sectors_per_fat_16 : bs->sectors_per_fat_32;
    
    data_sectors = total_sectors - (bs->reserved_sectors + 
                                   (bs->num_fats * sectors_per_fat));
    
    /* Calculate cluster count */
    cluster_count = data_sectors / bs->sectors_per_cluster;
    
    /* Determine FAT type based on cluster count */
    if (cluster_count < 4085) {
        return 12;
    } else if (cluster_count < 65525) {
        return 16;
    } else {
        return 32;
    }
}

/*
 * Mount FAT filesystem
 */
int fat_mount(void)
{
    printf("Mounting FAT filesystem...\n");
    
    memset(&fat_ctx, 0, sizeof(fat_ctx));
    fat_ctx.sector_buffer = sector_buffer;
    
    /* Read boot sector */
    int ret = read_sector(0, fat_ctx.sector_buffer);
    if (ret != IBOOY_SUCCESS) {
        printf("ERROR: Failed to read boot sector\n");
        return ret;
    }
    
    struct fat_boot_sector *bs = (struct fat_boot_sector *)fat_ctx.sector_buffer;
    
    /* Verify boot signature */
    if (fat_ctx.sector_buffer[510] != 0x55 || fat_ctx.sector_buffer[511] != 0xAA) {
        printf("ERROR: Invalid boot signature\n");
        return IBOOY_ERROR_INVALID;
    }
    
    /* Parse boot sector */
    fat_ctx.bytes_per_sector = bs->bytes_per_sector;
    fat_ctx.sectors_per_cluster = bs->sectors_per_cluster;
    fat_ctx.reserved_sectors = bs->reserved_sectors;
    fat_ctx.num_fats = bs->num_fats;
    
    /* Determine FAT type */
    fat_ctx.fat_type = determine_fat_type(bs);
    
    if (fat_ctx.fat_type == 32) {
        fat_ctx.sectors_per_fat = bs->sectors_per_fat_32;
        fat_ctx.root_cluster = bs->root_cluster;
    } else {
        fat_ctx.sectors_per_fat = bs->sectors_per_fat_16;
        fat_ctx.root_cluster = 0;  /* FAT12/16 use root directory area */
    }
    
    /* Calculate important sectors */
    fat_ctx.fat_start_sector = fat_ctx.reserved_sectors;
    fat_ctx.data_start_sector = fat_ctx.reserved_sectors + 
                                (fat_ctx.num_fats * fat_ctx.sectors_per_fat);
    
    fat_ctx.mounted = 1;
    
    printf("FAT%u filesystem mounted\n", fat_ctx.fat_type);
    printf("  Bytes per sector: %u\n", fat_ctx.bytes_per_sector);
    printf("  Sectors per cluster: %u\n", fat_ctx.sectors_per_cluster);
    printf("  Reserved sectors: %u\n", fat_ctx.reserved_sectors);
    printf("  Number of FATs: %u\n", fat_ctx.num_fats);
    printf("  Sectors per FAT: %u\n", fat_ctx.sectors_per_fat);
    
    return IBOOY_SUCCESS;
}

/*
 * Convert cluster number to sector number
 */
static uint32_t cluster_to_sector(uint32_t cluster)
{
    if (cluster < 2) {
        return 0;  /* Invalid cluster */
    }
    
    return fat_ctx.data_start_sector + ((cluster - 2) * fat_ctx.sectors_per_cluster);
}

/*
 * Read FAT entry
 */
static uint32_t read_fat_entry(uint32_t cluster)
{
    uint32_t fat_offset;
    uint32_t fat_sector;
    uint32_t entry_offset;
    
    /* Calculate FAT offset */
    if (fat_ctx.fat_type == 32) {
        fat_offset = cluster * 4;
    } else if (fat_ctx.fat_type == 16) {
        fat_offset = cluster * 2;
    } else {
        fat_offset = cluster + (cluster / 2);  /* FAT12 */
    }
    
    fat_sector = fat_ctx.fat_start_sector + (fat_offset / fat_ctx.bytes_per_sector);
    entry_offset = fat_offset % fat_ctx.bytes_per_sector;
    
    /* Read FAT sector */
    if (read_sector(fat_sector, fat_ctx.sector_buffer) != IBOOY_SUCCESS) {
        return 0;
    }
    
    /* Extract FAT entry */
    uint32_t next_cluster;
    if (fat_ctx.fat_type == 32) {
        next_cluster = *(uint32_t *)(fat_ctx.sector_buffer + entry_offset) & 0x0FFFFFFF;
    } else if (fat_ctx.fat_type == 16) {
        next_cluster = *(uint16_t *)(fat_ctx.sector_buffer + entry_offset);
    } else {
        /* FAT12 */
        uint16_t val = *(uint16_t *)(fat_ctx.sector_buffer + entry_offset);
        if (cluster & 1) {
            next_cluster = val >> 4;
        } else {
            next_cluster = val & 0x0FFF;
        }
    }
    
    return next_cluster;
}

/*
 * Check if cluster is end of chain
 */
static int is_end_of_chain(uint32_t cluster)
{
    if (fat_ctx.fat_type == 32) {
        return cluster >= 0x0FFFFFF8;
    } else if (fat_ctx.fat_type == 16) {
        return cluster >= 0xFFF8;
    } else {
        return cluster >= 0xFF8;
    }
}

/*
 * Find file in directory
 */
static int find_file_in_directory(uint32_t dir_cluster, const char *filename, 
                                  struct fat_dir_entry *entry)
{
    uint32_t current_cluster = dir_cluster;
    
    while (!is_end_of_chain(current_cluster)) {
        uint32_t sector = cluster_to_sector(current_cluster);
        
        /* Read all sectors in this cluster */
        for (uint32_t i = 0; i < fat_ctx.sectors_per_cluster; i++) {
            if (read_sector(sector + i, fat_ctx.sector_buffer) != IBOOY_SUCCESS) {
                return IBOOY_ERROR_IO;
            }
            
            /* Check all directory entries in this sector */
            struct fat_dir_entry *entries = (struct fat_dir_entry *)fat_ctx.sector_buffer;
            for (int j = 0; j < (fat_ctx.bytes_per_sector / sizeof(struct fat_dir_entry)); j++) {
                if (entries[j].name[0] == 0) {
                    /* End of directory */
                    return IBOOY_ERROR_NOTFOUND;
                }
                
                if (entries[j].name[0] == 0xE5) {
                    /* Deleted entry */
                    continue;
                }
                
                if (entries[j].attr & FAT_ATTR_LFN) {
                    /* Long filename entry */
                    continue;
                }
                
                /* TODO: Implement proper filename comparison */
                /* For now, just return the first valid file */
                if (!(entries[j].attr & FAT_ATTR_DIRECTORY) && 
                    !(entries[j].attr & FAT_ATTR_VOLUME_ID)) {
                    *entry = entries[j];
                    return IBOOY_SUCCESS;
                }
            }
        }
        
        /* Get next cluster */
        current_cluster = read_fat_entry(current_cluster);
    }
    
    return IBOOY_ERROR_NOTFOUND;
}

/*
 * Read file data
 */
int fat_read_file(const char *filename, void *buffer, size_t max_size, size_t *actual_size)
{
    if (!fat_ctx.mounted) {
        return IBOOY_ERROR_INVALID;
    }
    
    printf("Reading file: %s\n", filename);
    
    /* Find file in root directory */
    struct fat_dir_entry entry;
    int ret = find_file_in_directory(fat_ctx.root_cluster, filename, &entry);
    if (ret != IBOOY_SUCCESS) {
        printf("ERROR: File not found: %s\n", filename);
        return ret;
    }
    
    /* Get starting cluster */
    uint32_t cluster = ((uint32_t)entry.cluster_high << 16) | entry.cluster_low;
    uint32_t file_size = entry.size;
    uint32_t bytes_read = 0;
    uint8_t *dest = (uint8_t *)buffer;
    
    printf("Found file: size=%u, cluster=%u\n", file_size, cluster);
    
    if (file_size > max_size) {
        printf("ERROR: File too large: %u > %zu\n", file_size, max_size);
        return IBOOY_ERROR_INVALID;
    }
    
    /* Read file cluster by cluster */
    while (!is_end_of_chain(cluster) && bytes_read < file_size) {
        uint32_t sector = cluster_to_sector(cluster);
        
        /* Read all sectors in this cluster */
        for (uint32_t i = 0; i < fat_ctx.sectors_per_cluster && bytes_read < file_size; i++) {
            if (read_sector(sector + i, fat_ctx.sector_buffer) != IBOOY_SUCCESS) {
                return IBOOY_ERROR_IO;
            }
            
            /* Copy data */
            uint32_t bytes_to_copy = MIN(fat_ctx.bytes_per_sector, file_size - bytes_read);
            memcpy(dest + bytes_read, fat_ctx.sector_buffer, bytes_to_copy);
            bytes_read += bytes_to_copy;
        }
        
        /* Get next cluster */
        cluster = read_fat_entry(cluster);
    }
    
    *actual_size = bytes_read;
    printf("File read successfully: %u bytes\n", bytes_read);
    
    return IBOOY_SUCCESS;
}

/*
 * Check if FAT filesystem is mounted
 */
int fat_is_mounted(void)
{
    return fat_ctx.mounted;
}

/*
 * Unmount FAT filesystem
 */
void fat_unmount(void)
{
    memset(&fat_ctx, 0, sizeof(fat_ctx));
    printf("FAT filesystem unmounted\n");
}
