/*
 * iBooy Bootloader - IMG4 Format Support
 * 
 * This file implements IMG4 format parsing for Apple's signed images.
 */

#include "../include/config.h"
#include <stdint.h>
#include <stddef.h>

/* IMG4 header structure */
struct img4_header {
    uint32_t magic;         /* 'IMG4' */
    uint32_t length;        /* Total length */
} __attribute__((packed));

/* IMG4 tag structure */
struct img4_tag {
    uint32_t tag;           /* Tag identifier */
    uint32_t length;        /* Tag length */
} __attribute__((packed));

/* IMG4 tags */
#define IMG4_TAG_IMG4   0x34474D49  /* 'IMG4' */
#define IMG4_TAG_IM4P   0x50344D49  /* 'IM4P' */
#define IMG4_TAG_IM4M   0x4D344D49  /* 'IM4M' */
#define IMG4_TAG_IM4R   0x52344D49  /* 'IM4R' */

/* IM4P payload structure */
struct im4p_header {
    uint32_t magic;         /* 'IM4P' */
    uint32_t length;        /* Total length */
    uint32_t type;          /* Payload type */
    uint32_t version;       /* Version */
    uint32_t flags;         /* Flags */
    uint32_t data_length;   /* Data length */
} __attribute__((packed));

/* IMG4 context */
struct img4_context {
    uint32_t initialized;
    uint8_t *buffer;
    size_t buffer_size;
};

static struct img4_context img4_ctx;
static uint8_t img4_buffer[1024 * 1024];  /* 1MB buffer */

/* Forward declarations */
extern int fat_read_file(const char *filename, void *buffer, size_t max_size, size_t *actual_size);
extern int apfs_read_file(const char *filename, void *buffer, size_t max_size, size_t *actual_size);
extern int lzss_decompress(const void *src, size_t src_len, void *dst, size_t dst_len);

/*
 * Initialize IMG4 subsystem
 */
int img4_init(void)
{
    printf("Initializing IMG4 subsystem...\n");
    
    memset(&img4_ctx, 0, sizeof(img4_ctx));
    img4_ctx.buffer = img4_buffer;
    img4_ctx.buffer_size = sizeof(img4_buffer);
    img4_ctx.initialized = 1;
    
    printf("IMG4 subsystem initialized\n");
    return IBOOY_SUCCESS;
}

/*
 * Parse IMG4 header
 */
static int parse_img4_header(const void *data, size_t size, struct img4_header *header)
{
    if (size < sizeof(struct img4_header)) {
        return IBOOY_ERROR_INVALID;
    }
    
    const struct img4_header *hdr = (const struct img4_header *)data;
    
    if (hdr->magic != IMG4_TAG_IMG4) {
        printf("ERROR: Invalid IMG4 magic: 0x%x\n", hdr->magic);
        return IBOOY_ERROR_INVALID;
    }
    
    if (hdr->length > size) {
        printf("ERROR: IMG4 length exceeds buffer size\n");
        return IBOOY_ERROR_INVALID;
    }
    
    *header = *hdr;
    return IBOOY_SUCCESS;
}

/*
 * Find IMG4 tag
 */
static const void *find_img4_tag(const void *data, size_t size, uint32_t tag, uint32_t *tag_size)
{
    const uint8_t *ptr = (const uint8_t *)data;
    const uint8_t *end = ptr + size;
    
    /* Skip IMG4 header */
    ptr += sizeof(struct img4_header);
    
    while (ptr + sizeof(struct img4_tag) <= end) {
        const struct img4_tag *tag_hdr = (const struct img4_tag *)ptr;
        
        if (tag_hdr->tag == tag) {
            if (tag_size) {
                *tag_size = tag_hdr->length;
            }
            return ptr;
        }
        
        /* Move to next tag */
        ptr += sizeof(struct img4_tag) + tag_hdr->length;
        
        /* Align to 4-byte boundary */
        ptr = (const uint8_t *)ALIGN_UP((uintptr_t)ptr, 4);
    }
    
    return NULL;
}

/*
 * Extract IM4P payload
 */
static int extract_im4p_payload(const void *im4p_data, size_t im4p_size, 
                                void *payload_buffer, size_t buffer_size, 
                                size_t *payload_size)
{
    if (im4p_size < sizeof(struct im4p_header)) {
        return IBOOY_ERROR_INVALID;
    }
    
    const struct im4p_header *hdr = (const struct im4p_header *)im4p_data;
    
    if (hdr->magic != IMG4_TAG_IM4P) {
        printf("ERROR: Invalid IM4P magic: 0x%x\n", hdr->magic);
        return IBOOY_ERROR_INVALID;
    }
    
    if (hdr->data_length > buffer_size) {
        printf("ERROR: IM4P payload too large: %u > %zu\n", hdr->data_length, buffer_size);
        return IBOOY_ERROR_INVALID;
    }
    
    /* Copy payload data */
    const uint8_t *payload_data = (const uint8_t *)im4p_data + sizeof(struct im4p_header);
    memcpy(payload_buffer, payload_data, hdr->data_length);
    
    *payload_size = hdr->data_length;
    
    printf("IM4P payload extracted: %u bytes\n", hdr->data_length);
    printf("  Type: 0x%x\n", hdr->type);
    printf("  Version: 0x%x\n", hdr->version);
    printf("  Flags: 0x%x\n", hdr->flags);
    
    return IBOOY_SUCCESS;
}

/*
 * Load IMG4 file
 */
int img4_load(const char *path, uint64_t *addr, uint64_t *size)
{
    if (!img4_ctx.initialized) {
        img4_init();
    }
    
    printf("Loading IMG4 file: %s\n", path);
    
    /* Try to read from filesystem */
    size_t file_size;
    int ret = fat_read_file(path, img4_ctx.buffer, img4_ctx.buffer_size, &file_size);
    if (ret != IBOOY_SUCCESS) {
        /* Try APFS if FAT failed */
        ret = apfs_read_file(path, img4_ctx.buffer, img4_ctx.buffer_size, &file_size);
        if (ret != IBOOY_SUCCESS) {
            printf("ERROR: Failed to read IMG4 file: %s\n", path);
            return ret;
        }
    }
    
    printf("Read IMG4 file: %zu bytes\n", file_size);
    
    /* Parse IMG4 header */
    struct img4_header header;
    ret = parse_img4_header(img4_ctx.buffer, file_size, &header);
    if (ret != IBOOY_SUCCESS) {
        printf("ERROR: Failed to parse IMG4 header\n");
        return ret;
    }
    
    printf("IMG4 header parsed: length=%u\n", header.length);
    
    /* Find IM4P tag */
    uint32_t im4p_size;
    const void *im4p_data = find_img4_tag(img4_ctx.buffer, file_size, IMG4_TAG_IM4P, &im4p_size);
    if (!im4p_data) {
        printf("ERROR: IM4P tag not found\n");
        return IBOOY_ERROR_INVALID;
    }
    
    printf("Found IM4P tag: size=%u\n", im4p_size);
    
    /* Extract payload */
    size_t payload_size;
    ret = extract_im4p_payload(im4p_data, im4p_size, (void *)*addr, 
                               MAX_KERNEL_SIZE, &payload_size);
    if (ret != IBOOY_SUCCESS) {
        printf("ERROR: Failed to extract IM4P payload\n");
        return ret;
    }
    
    *size = payload_size;
    
    printf("IMG4 loaded successfully: %zu bytes at 0x%lx\n", payload_size, *addr);
    
    return IBOOY_SUCCESS;
}

/*
 * Verify IMG4 signature
 */
int img4_verify_signature(const void *img4_data, size_t size)
{
    printf("Verifying IMG4 signature...\n");
    
    /* Find IM4M tag (manifest) */
    uint32_t im4m_size;
    const void *im4m_data = find_img4_tag(img4_data, size, IMG4_TAG_IM4M, &im4m_size);
    if (!im4m_data) {
        printf("WARNING: No IM4M manifest found\n");
        return IBOOY_SUCCESS;  /* Allow unsigned images for now */
    }
    
    printf("Found IM4M manifest: size=%u\n", im4m_size);
    
    /* TODO: Implement actual signature verification */
    /* This would involve:
     * 1. Parsing the ASN.1 manifest
     * 2. Verifying the certificate chain
     * 3. Checking the signature against the payload hash
     */
    
    printf("IMG4 signature verification passed (simulated)\n");
    return IBOOY_SUCCESS;
}

/*
 * Check if data is IMG4 format
 */
int img4_is_valid(const void *data, size_t size)
{
    if (size < sizeof(struct img4_header)) {
        return 0;
    }
    
    const struct img4_header *hdr = (const struct img4_header *)data;
    return (hdr->magic == IMG4_TAG_IMG4);
}

/*
 * Get IMG4 payload type
 */
uint32_t img4_get_payload_type(const void *img4_data, size_t size)
{
    /* Find IM4P tag */
    uint32_t im4p_size;
    const void *im4p_data = find_img4_tag(img4_data, size, IMG4_TAG_IM4P, &im4p_size);
    if (!im4p_data) {
        return 0;
    }
    
    const struct im4p_header *hdr = (const struct im4p_header *)im4p_data;
    return hdr->type;
}
