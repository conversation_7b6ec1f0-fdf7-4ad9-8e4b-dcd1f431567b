/* iBooy Bootloader Linker Script */

ENTRY(_start)

MEMORY
{
    /* QEMU virt machine memory layout */
    RAM (rwx) : ORIGIN = 0x40000000, LENGTH = 128M
}

SECTIONS
{
    . = 0x40000000;
    
    /* Boot header and entry point */
    .text.boot : {
        KEEP(*(.text.boot))
        . = ALIGN(8);
    } > RAM
    
    /* Code section */
    .text : {
        *(.text)
        *(.text.*)
        . = ALIGN(8);
    } > RAM
    
    /* Read-only data */
    .rodata : {
        *(.rodata)
        *(.rodata.*)
        . = ALIGN(8);
    } > RAM
    
    /* Exception vectors */
    .vectors : {
        . = ALIGN(2048);  /* Exception vectors must be 2KB aligned */
        __vectors_start = .;
        *(.vectors)
        __vectors_end = .;
        . = ALIGN(8);
    } > RAM
    
    /* Initialized data */
    .data : {
        __data_start = .;
        *(.data)
        *(.data.*)
        __data_end = .;
        . = ALIGN(8);
    } > RAM
    
    /* Uninitialized data */
    .bss : {
        __bss_start = .;
        *(.bss)
        *(.bss.*)
        *(COMMON)
        __bss_end = .;
        . = ALIGN(8);
    } > RAM
    
    /* End of bootloader */
    __bootloader_end = .;

    /* Stack (placed right after bootloader) */
    . = ALIGN(16);
    __stack_start = .;
    . += 0x10000;  /* 64KB stack */
    __stack_end = .;

    /* Heap */
    . = ALIGN(16);
    __heap_start = .;
    . += 0x100000;  /* 1MB heap */
    __heap_end = .;

    /* Boot arguments */
    . = ALIGN(16);
    __boot_args_start = .;
    . += 0x1000;  /* 4KB for boot arguments */
    __boot_args_end = .;

    /* Device tree blob */
    . = ALIGN(16);
    __dtb_start = .;
    . += 0x10000;  /* 64KB for DTB */
    __dtb_end = .;

    /* Kernel load area */
    . = ALIGN(0x1000);  /* Page aligned */
    __kernel_start = .;
    . += 0x2000000;  /* 32MB for kernel */
    __kernel_end = .;
    
    /* End of bootloader */
    __bootloader_end = .;
    
    /* Discard sections */
    /DISCARD/ : {
        *(.comment)
        *(.note*)
        *(.eh_frame*)
        *(.debug*)
    }
}

/* Symbols for memory layout */
__bootloader_start = 0x40000000;
__bootloader_size = __bootloader_end - __bootloader_start;
__memory_start = 0x40000000;
__memory_end = 0x48000000;
__memory_size = __memory_end - __memory_start;
