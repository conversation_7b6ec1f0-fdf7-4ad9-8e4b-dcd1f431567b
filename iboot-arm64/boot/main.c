/*
 * iBooy Bootloader - Main Entry Point
 * 
 * This file contains the main C entry point and initialization sequence.
 */

#include "../include/config.h"
#include <stdint.h>
#include <stddef.h>

/* Forward declarations */
extern void uart_init(void);
extern void timer_init(void);
extern void gui_init(void);
extern void shell_init(void);
extern void dtb_loader_init(void);
extern void platform_init(void);
extern void patches_init(void);
extern void boot_kernel(void);
extern void cpu_print_info(void);
extern void exceptions_init(void);
extern int crypto_init(void);
extern int pac_init(void);

/* Simple printf implementation */
static char printf_buffer[1024];
static size_t printf_pos = 0;

extern void uart_putc(char c);
extern void uart_puts(const char *s);

void putchar(char c)
{
    uart_putc(c);
}

void puts(const char *s)
{
    uart_puts(s);
    uart_putc('\n');
}

int printf(const char *format, ...)
{
    /* Simple printf implementation - just handle %s, %d, %x, %c */
    __builtin_va_list args;
    __builtin_va_start(args, format);
    
    printf_pos = 0;
    const char *p = format;
    
    while (*p) {
        if (*p == '%' && *(p + 1)) {
            p++;
            switch (*p) {
            case 's': {
                const char *s = __builtin_va_arg(args, const char *);
                while (*s && printf_pos < sizeof(printf_buffer) - 1) {
                    printf_buffer[printf_pos++] = *s++;
                }
                break;
            }
            case 'd': {
                int val = __builtin_va_arg(args, int);
                char num_buf[32];
                int num_pos = 0;
                
                if (val < 0) {
                    printf_buffer[printf_pos++] = '-';
                    val = -val;
                }
                
                if (val == 0) {
                    printf_buffer[printf_pos++] = '0';
                } else {
                    while (val > 0) {
                        num_buf[num_pos++] = '0' + (val % 10);
                        val /= 10;
                    }
                    while (num_pos > 0 && printf_pos < sizeof(printf_buffer) - 1) {
                        printf_buffer[printf_pos++] = num_buf[--num_pos];
                    }
                }
                break;
            }
            case 'x':
            case 'X': {
                unsigned int val = __builtin_va_arg(args, unsigned int);
                char hex_digits[] = "0123456789abcdef";
                char num_buf[32];
                int num_pos = 0;
                
                if (val == 0) {
                    printf_buffer[printf_pos++] = '0';
                } else {
                    while (val > 0) {
                        num_buf[num_pos++] = hex_digits[val & 0xf];
                        val >>= 4;
                    }
                    while (num_pos > 0 && printf_pos < sizeof(printf_buffer) - 1) {
                        printf_buffer[printf_pos++] = num_buf[--num_pos];
                    }
                }
                break;
            }
            case 'l': {
                if (*(p + 1) == 'x') {
                    p++; /* Skip the 'x' */
                    unsigned long val = __builtin_va_arg(args, unsigned long);
                    char hex_digits[] = "0123456789abcdef";
                    char num_buf[64];
                    int num_pos = 0;
                    
                    if (val == 0) {
                        printf_buffer[printf_pos++] = '0';
                    } else {
                        while (val > 0) {
                            num_buf[num_pos++] = hex_digits[val & 0xf];
                            val >>= 4;
                        }
                        while (num_pos > 0 && printf_pos < sizeof(printf_buffer) - 1) {
                            printf_buffer[printf_pos++] = num_buf[--num_pos];
                        }
                    }
                }
                break;
            }
            case 'c': {
                char c = (char)__builtin_va_arg(args, int);
                if (printf_pos < sizeof(printf_buffer) - 1) {
                    printf_buffer[printf_pos++] = c;
                }
                break;
            }
            case '%':
                if (printf_pos < sizeof(printf_buffer) - 1) {
                    printf_buffer[printf_pos++] = '%';
                }
                break;
            default:
                if (printf_pos < sizeof(printf_buffer) - 1) {
                    printf_buffer[printf_pos++] = *p;
                }
                break;
            }
        } else {
            if (printf_pos < sizeof(printf_buffer) - 1) {
                printf_buffer[printf_pos++] = *p;
            }
        }
        p++;
    }
    
    __builtin_va_end(args);
    
    /* Null terminate and output */
    printf_buffer[printf_pos] = '\0';
    uart_puts(printf_buffer);
    
    return printf_pos;
}

/*
 * Print boot banner
 */
static void print_banner(void)
{
    printf("\n");
    printf("  _  ____                   \n");
    printf(" (_)|  _ \\  ___   ___  _   _ \n");
    printf(" | || |_) |/ _ \\ / _ \\| | | |\n");
    printf(" | ||  _ <| (_) | (_) | |_| |\n");
    printf(" |_||_| \\_\\\\___/ \\___/ \\__, |\n");
    printf("                      |___/ \n");
    printf("\n");
    printf("iBooy Bootloader v%s\n", IBOOY_VERSION_STRING);
    printf("ARM64 Custom Bootloader\n");
    printf("Built: %s %s\n", __DATE__, __TIME__);
    printf("\n");
}

/*
 * Print memory layout
 */
static void print_memory_layout(void)
{
    extern char __bootloader_start[], __bootloader_end[];
    extern char __stack_start[], __stack_end[];
    extern char __heap_start[], __heap_end[];
    extern char __kernel_start[], __kernel_end[];
    
    printf("Memory Layout:\n");
    printf("  Bootloader: 0x%lx - 0x%lx (%lu KB)\n",
           (unsigned long)__bootloader_start,
           (unsigned long)__bootloader_end,
           ((unsigned long)__bootloader_end - (unsigned long)__bootloader_start) / 1024);
    
    printf("  Stack:      0x%lx - 0x%lx (%lu KB)\n",
           (unsigned long)__stack_start,
           (unsigned long)__stack_end,
           ((unsigned long)__stack_end - (unsigned long)__stack_start) / 1024);
    
    printf("  Heap:       0x%lx - 0x%lx (%lu KB)\n",
           (unsigned long)__heap_start,
           (unsigned long)__heap_end,
           ((unsigned long)__heap_end - (unsigned long)__heap_start) / 1024);
    
    printf("  Kernel:     0x%lx - 0x%lx (%lu MB)\n",
           (unsigned long)__kernel_start,
           (unsigned long)__kernel_end,
           ((unsigned long)__kernel_end - (unsigned long)__kernel_start) / (1024 * 1024));
    
    printf("\n");
}

/*
 * Initialize all subsystems
 */
static int init_subsystems(void)
{
    printf("Initializing subsystems...\n");
    
    /* Initialize exception handling */
    exceptions_init();

    /* Initialize crypto subsystem */
    crypto_init();
    printf("  Crypto: OK\n");

    /* Initialize PAC */
    pac_init();
    printf("  PAC: OK\n");

    /* Initialize timer */
    timer_init();
    printf("  Timer: OK\n");

    /* Initialize platform-specific code */
    platform_init();
    printf("  Platform: OK\n");

    /* Initialize device tree loader */
    dtb_loader_init();
    printf("  DTB Loader: OK\n");

    /* Initialize patches system */
    patches_init();
    printf("  Patches: OK\n");
    
    /* Initialize GUI if enabled */
    if (GUI_ENABLED) {
        gui_init();
        printf("  GUI: OK\n");
    }
    
    /* Initialize shell if enabled */
    if (SHELL_ENABLED) {
        shell_init();
        printf("  Shell: OK\n");
    }
    
    printf("All subsystems initialized successfully\n\n");
    return IBOOY_SUCCESS;
}

/*
 * Main entry point from assembly
 */
int main(void)
{
    /* Initialize UART first for debug output */
    uart_init();
    
    /* Print boot banner */
    print_banner();
    
    /* Print CPU information */
    cpu_print_info();
    
    /* Print memory layout */
    if (VERBOSE_BOOT) {
        print_memory_layout();
    }
    
    /* Initialize all subsystems */
    if (init_subsystems() != IBOOY_SUCCESS) {
        printf("ERROR: Failed to initialize subsystems\n");
        goto error;
    }
    
    /* Boot the kernel */
    printf("Starting kernel boot process...\n");
    boot_kernel();
    
    /* Should never reach here */
    printf("ERROR: Kernel boot failed\n");
    
error:
    printf("Boot failed, entering shell...\n");
    
    /* Enter interactive shell if enabled */
    if (SHELL_ENABLED) {
        extern void shell_run(void);
        shell_run();
    }
    
    /* Hang if shell is not enabled */
    printf("System halted\n");
    while (1) {
        __asm__ volatile("wfi");
    }
    
    return IBOOY_ERROR_GENERIC;
}

/*
 * Panic function
 */
void panic(const char *message)
{
    printf("\nPANIC: %s\n", message);
    printf("System halted\n");
    
    while (1) {
        __asm__ volatile("wfi");
    }
}

/*
 * Assert function
 */
void assert_failed(const char *file, int line, const char *func, const char *expr)
{
    printf("\nASSERT FAILED: %s:%d in %s(): %s\n", file, line, func, expr);
    panic("Assertion failed");
}

/* Simple memory functions */
void *memset(void *s, int c, size_t n)
{
    unsigned char *p = s;
    while (n--) {
        *p++ = (unsigned char)c;
    }
    return s;
}

void *memcpy(void *dest, const void *src, size_t n)
{
    unsigned char *d = dest;
    const unsigned char *s = src;
    while (n--) {
        *d++ = *s++;
    }
    return dest;
}

int memcmp(const void *s1, const void *s2, size_t n)
{
    const unsigned char *p1 = s1;
    const unsigned char *p2 = s2;
    while (n--) {
        if (*p1 != *p2) {
            return *p1 - *p2;
        }
        p1++;
        p2++;
    }
    return 0;
}

size_t strlen(const char *s)
{
    size_t len = 0;
    while (*s++) {
        len++;
    }
    return len;
}

char *strcpy(char *dest, const char *src)
{
    char *d = dest;
    while ((*d++ = *src++));
    return dest;
}

int strcmp(const char *s1, const char *s2)
{
    while (*s1 && *s1 == *s2) {
        s1++;
        s2++;
    }
    return *(unsigned char *)s1 - *(unsigned char *)s2;
}

char *strcat(char *dest, const char *src)
{
    char *d = dest;
    while (*d) d++;
    while ((*d++ = *src++));
    return dest;
}

int sprintf(char *str, const char *format, ...)
{
    /* Simple sprintf - just handle basic cases */
    __builtin_va_list args;
    __builtin_va_start(args, format);

    char *s = str;
    const char *p = format;

    while (*p) {
        if (*p == '%' && *(p + 1)) {
            p++;
            switch (*p) {
            case 'l':
                if (*(p + 1) == 'x') {
                    p++;
                    unsigned long val = __builtin_va_arg(args, unsigned long);
                    char hex_digits[] = "0123456789abcdef";
                    char buf[32];
                    int pos = 0;
                    if (val == 0) {
                        *s++ = '0';
                    } else {
                        while (val > 0) {
                            buf[pos++] = hex_digits[val & 0xf];
                            val >>= 4;
                        }
                        while (pos > 0) {
                            *s++ = buf[--pos];
                        }
                    }
                }
                break;
            default:
                *s++ = *p;
                break;
            }
        } else {
            *s++ = *p;
        }
        p++;
    }

    __builtin_va_end(args);
    *s = '\0';
    return s - str;
}
