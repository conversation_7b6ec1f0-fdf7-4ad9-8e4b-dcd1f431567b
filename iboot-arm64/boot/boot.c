/*
 * iBooy Bootloader - Boot Logic
 * 
 * This file implements the main boot logic and kernel loading sequence.
 */

#include "../include/config.h"
#include <stdint.h>
#include <stddef.h>

/* Boot stages */
enum boot_stage {
    BOOT_STAGE_INIT,
    BOOT_STAGE_DETECT_MEDIA,
    BOOT_STAGE_MOUNT_FS,
    BOOT_STAGE_LOAD_DTB,
    BOOT_STAGE_LOAD_KERNEL,
    BOOT_STAGE_LOAD_RAMDISK,
    BOOT_STAGE_VERIFY,
    BOOT_STAGE_PREPARE_HANDOFF,
    BOOT_STAGE_HANDOFF,
    BOOT_STAGE_FAILED
};

/* Boot context */
struct boot_context {
    enum boot_stage stage;
    uint32_t boot_source;
    uint64_t kernel_addr;
    uint64_t kernel_size;
    uint64_t dtb_addr;
    uint64_t dtb_size;
    uint64_t ramdisk_addr;
    uint64_t ramdisk_size;
    char boot_args[MAX_BOOT_ARGS_SIZE];
    uint32_t boot_flags;
};

static struct boot_context boot_ctx;

/* Forward declarations */
extern int fat_mount(void);
extern int apfs_mount(void);
extern int img4_load(const char *path, uint64_t *addr, uint64_t *size);
extern int dtb_load(const char *path, uint64_t addr);
extern int crypto_verify_signature(const void *data, size_t size, const void *sig);
extern void platform_prepare_kernel_handoff(void);
extern void platform_jump_to_kernel(uint64_t kernel_addr, uint64_t dtb_addr, const char *boot_args);
extern int sprintf(char *str, const char *format, ...);
extern char *strcat(char *dest, const char *src);

/*
 * Get boot stage name
 */
static const char *get_stage_name(enum boot_stage stage)
{
    switch (stage) {
    case BOOT_STAGE_INIT:           return "INIT";
    case BOOT_STAGE_DETECT_MEDIA:   return "DETECT_MEDIA";
    case BOOT_STAGE_MOUNT_FS:       return "MOUNT_FS";
    case BOOT_STAGE_LOAD_DTB:       return "LOAD_DTB";
    case BOOT_STAGE_LOAD_KERNEL:    return "LOAD_KERNEL";
    case BOOT_STAGE_LOAD_RAMDISK:   return "LOAD_RAMDISK";
    case BOOT_STAGE_VERIFY:         return "VERIFY";
    case BOOT_STAGE_PREPARE_HANDOFF: return "PREPARE_HANDOFF";
    case BOOT_STAGE_HANDOFF:        return "HANDOFF";
    case BOOT_STAGE_FAILED:         return "FAILED";
    default:                        return "UNKNOWN";
    }
}

/*
 * Set boot stage
 */
static void set_boot_stage(enum boot_stage stage)
{
    boot_ctx.stage = stage;
    if (VERBOSE_BOOT) {
        printf("Boot stage: %s\n", get_stage_name(stage));
    }
}

/*
 * Detect boot media
 */
static int detect_boot_media(void)
{
    set_boot_stage(BOOT_STAGE_DETECT_MEDIA);
    
    /* For now, assume eMMC/SD card */
    boot_ctx.boot_source = DEFAULT_BOOT_SOURCE;
    
    printf("Boot source: ");
    switch (boot_ctx.boot_source) {
    case BOOT_SOURCE_EMMC:
        printf("eMMC/SD\n");
        break;
    case BOOT_SOURCE_USB:
        printf("USB\n");
        break;
    case BOOT_SOURCE_NETWORK:
        printf("Network\n");
        break;
    case BOOT_SOURCE_TFTP:
        printf("TFTP\n");
        break;
    default:
        printf("Unknown\n");
        return IBOOY_ERROR_NOTFOUND;
    }
    
    return IBOOY_SUCCESS;
}

/*
 * Mount filesystem
 */
static int mount_filesystem(void)
{
    set_boot_stage(BOOT_STAGE_MOUNT_FS);
    
    int ret;
    
    /* Try FAT first */
    if (FS_SUPPORT_FAT) {
        ret = fat_mount();
        if (ret == IBOOY_SUCCESS) {
            printf("Mounted FAT filesystem\n");
            return IBOOY_SUCCESS;
        }
    }
    
    /* Try APFS */
    if (FS_SUPPORT_APFS) {
        ret = apfs_mount();
        if (ret == IBOOY_SUCCESS) {
            printf("Mounted APFS filesystem\n");
            return IBOOY_SUCCESS;
        }
    }
    
    printf("ERROR: No supported filesystem found\n");
    return IBOOY_ERROR_NOTFOUND;
}

/*
 * Load device tree blob
 */
static int load_device_tree(void)
{
    set_boot_stage(BOOT_STAGE_LOAD_DTB);
    
    boot_ctx.dtb_addr = DTB_LOAD_ADDR;
    
    /* Try to load DTB from filesystem */
    int ret = dtb_load("dtb/qemu-arm64.dtb", boot_ctx.dtb_addr);
    if (ret != IBOOY_SUCCESS) {
        printf("WARNING: Failed to load DTB from filesystem, using built-in\n");
        /* Use built-in DTB or generate one */
        boot_ctx.dtb_size = 0x1000;  /* Placeholder */
    } else {
        printf("Loaded DTB from filesystem\n");
    }
    
    return IBOOY_SUCCESS;
}

/*
 * Load kernel image
 */
static int load_kernel_image(void)
{
    set_boot_stage(BOOT_STAGE_LOAD_KERNEL);
    
    boot_ctx.kernel_addr = KERNEL_LOAD_ADDR;
    
    int ret;
    
    /* Try to load kernel as IMG4 */
    if (FS_SUPPORT_IMG4) {
        ret = img4_load("kernelcache", &boot_ctx.kernel_addr, &boot_ctx.kernel_size);
        if (ret == IBOOY_SUCCESS) {
            printf("Loaded kernel from IMG4: %lu bytes at 0x%lx\n", 
                   boot_ctx.kernel_size, boot_ctx.kernel_addr);
            return IBOOY_SUCCESS;
        }
    }
    
    /* Try to load raw kernel */
    ret = img4_load("Image", &boot_ctx.kernel_addr, &boot_ctx.kernel_size);
    if (ret == IBOOY_SUCCESS) {
        printf("Loaded raw kernel: %lu bytes at 0x%lx\n", 
               boot_ctx.kernel_size, boot_ctx.kernel_addr);
        return IBOOY_SUCCESS;
    }
    
    printf("ERROR: Failed to load kernel image\n");
    return IBOOY_ERROR_NOTFOUND;
}

/*
 * Load ramdisk
 */
static int load_ramdisk_image(void)
{
    set_boot_stage(BOOT_STAGE_LOAD_RAMDISK);
    
    boot_ctx.ramdisk_addr = RAMDISK_LOAD_ADDR;
    
    /* Try to load ramdisk */
    int ret = img4_load("ramdisk", &boot_ctx.ramdisk_addr, &boot_ctx.ramdisk_size);
    if (ret == IBOOY_SUCCESS) {
        printf("Loaded ramdisk: %lu bytes at 0x%lx\n", 
               boot_ctx.ramdisk_size, boot_ctx.ramdisk_addr);
    } else {
        printf("No ramdisk found (optional)\n");
        boot_ctx.ramdisk_addr = 0;
        boot_ctx.ramdisk_size = 0;
    }
    
    return IBOOY_SUCCESS;
}

/*
 * Verify loaded images
 */
static int verify_images(void)
{
    set_boot_stage(BOOT_STAGE_VERIFY);
    
    if (!SECURE_BOOT_ENABLED) {
        printf("Secure boot disabled, skipping verification\n");
        return IBOOY_SUCCESS;
    }
    
    printf("Verifying kernel signature...\n");
    
    /* Verify kernel signature */
    int ret = crypto_verify_signature((void *)boot_ctx.kernel_addr, 
                                      boot_ctx.kernel_size, NULL);
    if (ret != IBOOY_SUCCESS) {
        printf("ERROR: Kernel signature verification failed\n");
        return IBOOY_ERROR_AUTH;
    }
    
    printf("Kernel signature verified\n");
    
    /* Verify ramdisk if present */
    if (boot_ctx.ramdisk_size > 0) {
        printf("Verifying ramdisk signature...\n");
        ret = crypto_verify_signature((void *)boot_ctx.ramdisk_addr, 
                                      boot_ctx.ramdisk_size, NULL);
        if (ret != IBOOY_SUCCESS) {
            printf("ERROR: Ramdisk signature verification failed\n");
            return IBOOY_ERROR_AUTH;
        }
        printf("Ramdisk signature verified\n");
    }
    
    return IBOOY_SUCCESS;
}

/*
 * Prepare kernel handoff
 */
static int prepare_kernel_handoff(void)
{
    set_boot_stage(BOOT_STAGE_PREPARE_HANDOFF);
    
    /* Set up boot arguments */
    strcpy(boot_ctx.boot_args, "console=ttyAMA0,115200 earlycon");
    
    if (boot_ctx.ramdisk_size > 0) {
        char ramdisk_arg[64];
        sprintf(ramdisk_arg, " initrd=0x%lx,0x%lx", 
                boot_ctx.ramdisk_addr, boot_ctx.ramdisk_size);
        strcat(boot_ctx.boot_args, ramdisk_arg);
    }
    
    printf("Boot args: %s\n", boot_ctx.boot_args);
    
    /* Platform-specific preparation */
    platform_prepare_kernel_handoff();
    
    return IBOOY_SUCCESS;
}

/*
 * Hand off to kernel
 */
static void handoff_to_kernel(void)
{
    set_boot_stage(BOOT_STAGE_HANDOFF);
    
    printf("Jumping to kernel at 0x%lx...\n", boot_ctx.kernel_addr);
    printf("DTB at 0x%lx, size %lu\n", boot_ctx.dtb_addr, boot_ctx.dtb_size);
    
    /* Disable interrupts */
    __asm__ volatile("msr daifset, #0xf");
    
    /* Clean and invalidate caches */
    __asm__ volatile("ic iallu");
    __asm__ volatile("dsb sy");
    __asm__ volatile("isb");
    
    /* Jump to kernel */
    platform_jump_to_kernel(boot_ctx.kernel_addr, boot_ctx.dtb_addr, boot_ctx.boot_args);
    
    /* Should never reach here */
    panic("Kernel handoff failed");
}

/*
 * Main boot function
 */
void boot_kernel(void)
{
    int ret;
    
    /* Initialize boot context */
    memset(&boot_ctx, 0, sizeof(boot_ctx));
    set_boot_stage(BOOT_STAGE_INIT);
    
    printf("Starting boot sequence...\n");
    
    /* Detect boot media */
    ret = detect_boot_media();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Mount filesystem */
    ret = mount_filesystem();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Load device tree */
    ret = load_device_tree();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Load kernel */
    ret = load_kernel_image();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Load ramdisk (optional) */
    ret = load_ramdisk_image();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Verify images */
    ret = verify_images();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Prepare handoff */
    ret = prepare_kernel_handoff();
    if (ret != IBOOY_SUCCESS) {
        goto boot_failed;
    }
    
    /* Hand off to kernel */
    handoff_to_kernel();
    
    /* Should never reach here */
    return;
    
boot_failed:
    set_boot_stage(BOOT_STAGE_FAILED);
    printf("Boot failed at stage: %s\n", get_stage_name(boot_ctx.stage));
}

/*
 * Get boot context
 */
const struct boot_context *boot_get_context(void)
{
    return &boot_ctx;
}
