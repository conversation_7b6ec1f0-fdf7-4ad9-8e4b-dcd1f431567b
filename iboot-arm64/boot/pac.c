/*
 * iBooy Bootloader - Pointer Authentication Code (PAC)
 * 
 * This file implements ARM64 Pointer Authentication support.
 */

#include "../include/config.h"
#include <stdint.h>
#include <stddef.h>

/* PAC key types */
enum pac_key_type {
    PAC_KEY_IA,     /* Instruction pointer authentication key A */
    PAC_KEY_IB,     /* Instruction pointer authentication key B */
    PAC_KEY_DA,     /* Data pointer authentication key A */
    PAC_KEY_DB,     /* Data pointer authentication key B */
    PAC_KEY_GA      /* Generic authentication key */
};

/* PAC context */
struct pac_context {
    uint32_t enabled;
    uint32_t keys_initialized;
    uint64_t key_ia_lo, key_ia_hi;
    uint64_t key_ib_lo, key_ib_hi;
    uint64_t key_da_lo, key_da_hi;
    uint64_t key_db_lo, key_db_hi;
    uint64_t key_ga_lo, key_ga_hi;
};

static struct pac_context pac_ctx;

/* Forward declarations */
extern int crypto_random_bytes(uint8_t *buffer, size_t length);
extern const struct cpu_features *cpu_get_features(void);

/*
 * Check if PAC is supported by CPU
 */
static int pac_is_supported(void)
{
    const struct cpu_features *features = cpu_get_features();
    
    /* Check ID_AA64ISAR1_EL1 for PAC support */
    uint64_t apa = (features->id_aa64isar1 >> 4) & 0xf;
    uint64_t api = (features->id_aa64isar1 >> 8) & 0xf;
    uint64_t gpa = (features->id_aa64isar1 >> 24) & 0xf;
    uint64_t gpi = (features->id_aa64isar1 >> 28) & 0xf;
    
    return (apa != 0) || (api != 0) || (gpa != 0) || (gpi != 0);
}

/*
 * Generate random PAC key
 */
static int generate_pac_key(uint64_t *key_lo, uint64_t *key_hi)
{
    uint8_t key_bytes[16];
    
    int ret = crypto_random_bytes(key_bytes, sizeof(key_bytes));
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    
    *key_lo = ((uint64_t)key_bytes[0] << 0) |
              ((uint64_t)key_bytes[1] << 8) |
              ((uint64_t)key_bytes[2] << 16) |
              ((uint64_t)key_bytes[3] << 24) |
              ((uint64_t)key_bytes[4] << 32) |
              ((uint64_t)key_bytes[5] << 40) |
              ((uint64_t)key_bytes[6] << 48) |
              ((uint64_t)key_bytes[7] << 56);
    
    *key_hi = ((uint64_t)key_bytes[8] << 0) |
              ((uint64_t)key_bytes[9] << 8) |
              ((uint64_t)key_bytes[10] << 16) |
              ((uint64_t)key_bytes[11] << 24) |
              ((uint64_t)key_bytes[12] << 32) |
              ((uint64_t)key_bytes[13] << 40) |
              ((uint64_t)key_bytes[14] << 48) |
              ((uint64_t)key_bytes[15] << 56);
    
    return IBOOY_SUCCESS;
}

/*
 * Set PAC key in system registers
 */
static void set_pac_key(enum pac_key_type key_type, uint64_t key_lo, uint64_t key_hi)
{
    switch (key_type) {
    case PAC_KEY_IA:
        __asm__ volatile("msr APIAKeyLo_EL1, %0" : : "r" (key_lo));
        __asm__ volatile("msr APIAKeyHi_EL1, %0" : : "r" (key_hi));
        break;
    case PAC_KEY_IB:
        __asm__ volatile("msr APIBKeyLo_EL1, %0" : : "r" (key_lo));
        __asm__ volatile("msr APIBKeyHi_EL1, %0" : : "r" (key_hi));
        break;
    case PAC_KEY_DA:
        __asm__ volatile("msr APDAKeyLo_EL1, %0" : : "r" (key_lo));
        __asm__ volatile("msr APDAKeyHi_EL1, %0" : : "r" (key_hi));
        break;
    case PAC_KEY_DB:
        __asm__ volatile("msr APDBKeyLo_EL1, %0" : : "r" (key_lo));
        __asm__ volatile("msr APDBKeyHi_EL1, %0" : : "r" (key_hi));
        break;
    case PAC_KEY_GA:
        __asm__ volatile("msr APGAKeyLo_EL1, %0" : : "r" (key_lo));
        __asm__ volatile("msr APGAKeyHi_EL1, %0" : : "r" (key_hi));
        break;
    }
    
    __asm__ volatile("isb");
}

/*
 * Initialize PAC keys
 */
static int init_pac_keys(void)
{
    int ret;
    
    printf("Generating PAC keys...\n");
    
    /* Generate IA key */
    ret = generate_pac_key(&pac_ctx.key_ia_lo, &pac_ctx.key_ia_hi);
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    set_pac_key(PAC_KEY_IA, pac_ctx.key_ia_lo, pac_ctx.key_ia_hi);
    
    /* Generate IB key */
    ret = generate_pac_key(&pac_ctx.key_ib_lo, &pac_ctx.key_ib_hi);
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    set_pac_key(PAC_KEY_IB, pac_ctx.key_ib_lo, pac_ctx.key_ib_hi);
    
    /* Generate DA key */
    ret = generate_pac_key(&pac_ctx.key_da_lo, &pac_ctx.key_da_hi);
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    set_pac_key(PAC_KEY_DA, pac_ctx.key_da_lo, pac_ctx.key_da_hi);
    
    /* Generate DB key */
    ret = generate_pac_key(&pac_ctx.key_db_lo, &pac_ctx.key_db_hi);
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    set_pac_key(PAC_KEY_DB, pac_ctx.key_db_lo, pac_ctx.key_db_hi);
    
    /* Generate GA key */
    ret = generate_pac_key(&pac_ctx.key_ga_lo, &pac_ctx.key_ga_hi);
    if (ret != IBOOY_SUCCESS) {
        return ret;
    }
    set_pac_key(PAC_KEY_GA, pac_ctx.key_ga_lo, pac_ctx.key_ga_hi);
    
    pac_ctx.keys_initialized = 1;
    printf("PAC keys initialized\n");
    
    return IBOOY_SUCCESS;
}

/*
 * Enable PAC in SCTLR_EL1
 */
static void enable_pac_sctlr(void)
{
    uint64_t sctlr;
    
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    
    sctlr |= (1UL << 30);  /* EnIA - Enable pointer authentication for IA */
    sctlr |= (1UL << 27);  /* EnIB - Enable pointer authentication for IB */
    sctlr |= (1UL << 13);  /* EnDA - Enable pointer authentication for DA */
    sctlr |= (1UL << 12);  /* EnDB - Enable pointer authentication for DB */
    
    __asm__ volatile("msr sctlr_el1, %0" : : "r" (sctlr));
    __asm__ volatile("isb");
    
    printf("PAC enabled in SCTLR_EL1\n");
}

/*
 * Initialize PAC subsystem
 */
int pac_init(void)
{
    printf("Initializing Pointer Authentication...\n");
    
    memset(&pac_ctx, 0, sizeof(pac_ctx));
    
    if (!PAC_ENABLED) {
        printf("PAC disabled in configuration\n");
        return IBOOY_SUCCESS;
    }
    
    if (!pac_is_supported()) {
        printf("PAC not supported by CPU\n");
        return IBOOY_SUCCESS;
    }
    
    /* Initialize PAC keys */
    int ret = init_pac_keys();
    if (ret != IBOOY_SUCCESS) {
        printf("ERROR: Failed to initialize PAC keys\n");
        return ret;
    }
    
    /* Enable PAC */
    enable_pac_sctlr();
    
    pac_ctx.enabled = 1;
    printf("Pointer Authentication enabled\n");
    
    return IBOOY_SUCCESS;
}

/*
 * Sign pointer with IA key
 */
uint64_t pac_sign_ia(uint64_t ptr, uint64_t modifier)
{
    if (!pac_ctx.enabled) {
        return ptr;
    }
    
    uint64_t result;
    __asm__ volatile("pacia %0, %1" : "=r" (result) : "r" (ptr), "0" (modifier));
    return result;
}

/*
 * Authenticate pointer with IA key
 */
uint64_t pac_auth_ia(uint64_t ptr, uint64_t modifier)
{
    if (!pac_ctx.enabled) {
        return ptr;
    }
    
    uint64_t result;
    __asm__ volatile("autia %0, %1" : "=r" (result) : "r" (ptr), "0" (modifier));
    return result;
}

/*
 * Sign pointer with DA key
 */
uint64_t pac_sign_da(uint64_t ptr, uint64_t modifier)
{
    if (!pac_ctx.enabled) {
        return ptr;
    }
    
    uint64_t result;
    __asm__ volatile("pacda %0, %1" : "=r" (result) : "r" (ptr), "0" (modifier));
    return result;
}

/*
 * Authenticate pointer with DA key
 */
uint64_t pac_auth_da(uint64_t ptr, uint64_t modifier)
{
    if (!pac_ctx.enabled) {
        return ptr;
    }
    
    uint64_t result;
    __asm__ volatile("autda %0, %1" : "=r" (result) : "r" (ptr), "0" (modifier));
    return result;
}

/*
 * Strip PAC from pointer
 */
uint64_t pac_strip(uint64_t ptr)
{
    uint64_t result;
    __asm__ volatile("xpaci %0" : "=r" (result) : "0" (ptr));
    return result;
}

/*
 * Check if PAC is enabled
 */
int pac_is_enabled(void)
{
    return pac_ctx.enabled;
}

/*
 * Get PAC context
 */
const struct pac_context *pac_get_context(void)
{
    return &pac_ctx;
}

/*
 * Disable PAC (for kernel handoff)
 */
void pac_disable(void)
{
    if (!pac_ctx.enabled) {
        return;
    }
    
    uint64_t sctlr;
    
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    
    sctlr &= ~(1UL << 30);  /* Disable EnIA */
    sctlr &= ~(1UL << 27);  /* Disable EnIB */
    sctlr &= ~(1UL << 13);  /* Disable EnDA */
    sctlr &= ~(1UL << 12);  /* Disable EnDB */
    
    __asm__ volatile("msr sctlr_el1, %0" : : "r" (sctlr));
    __asm__ volatile("isb");
    
    pac_ctx.enabled = 0;
    printf("PAC disabled\n");
}
