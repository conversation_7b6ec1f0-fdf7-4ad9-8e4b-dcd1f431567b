/*
 * iBooy Bootloader - QEMU ARM64 Platform Support
 */

#include "../include/config.h"
#include <stdint.h>

void platform_init(void)
{
    printf("QEMU ARM64 platform initialized\n");
}

void platform_prepare_kernel_handoff(void)
{
    printf("Preparing kernel handoff for QEMU\n");
}

void platform_jump_to_kernel(uint64_t kernel_addr, uint64_t dtb_addr, const char *boot_args)
{
    printf("Jumping to kernel at 0x%lx\n", kernel_addr);
    printf("DTB at 0x%lx\n", dtb_addr);
    printf("Boot args: %s\n", boot_args);
    
    /* For simulation, just hang */
    printf("Kernel handoff complete (simulated)\n");
    while (1) __asm__ volatile("wfi");
}
