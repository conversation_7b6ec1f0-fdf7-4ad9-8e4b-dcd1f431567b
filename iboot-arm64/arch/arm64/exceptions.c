/*
 * iBooy Bootloader - ARM64 Exception Handling
 * 
 * This file implements exception handlers for ARM64.
 */

#include "../include/config.h"

/* Exception Syndrome Register (ESR) bits */
#define ESR_EL1_EC_SHIFT        26
#define ESR_EL1_EC_MASK         0x3f
#define ESR_EL1_IL              (1 << 25)
#define ESR_EL1_ISS_MASK        0x1ffffff

/* Exception Classes */
#define EC_UNKNOWN              0x00
#define EC_WFI_WFE              0x01
#define EC_MCR_MRC_CP15         0x03
#define EC_MCRR_MRRC_CP15       0x04
#define EC_MCR_MRC_CP14         0x05
#define EC_LDC_STC_CP14         0x06
#define EC_FP_ASIMD             0x07
#define EC_MCR_MRC_CP10         0x08
#define EC_MCRR_MRRC_CP14       0x0c
#define EC_ILLEGAL_STATE        0x0e
#define EC_SVC32                0x11
#define EC_HVC32                0x12
#define EC_SMC32                0x13
#define EC_SVC64                0x15
#define EC_HVC64                0x16
#define EC_SMC64                0x17
#define EC_SYS64                0x18
#define EC_IMP_DEF              0x1f
#define EC_IABT_LOW             0x20
#define EC_IABT_CUR             0x21
#define EC_PC_ALIGN             0x22
#define EC_DABT_LOW             0x24
#define EC_DABT_CUR             0x25
#define EC_SP_ALIGN             0x26
#define EC_FP_EXC32             0x28
#define EC_FP_EXC64             0x2c
#define EC_SERROR               0x2f
#define EC_BREAKPT_LOW          0x30
#define EC_BREAKPT_CUR          0x31
#define EC_SOFTSTP_LOW          0x32
#define EC_SOFTSTP_CUR          0x33
#define EC_WATCHPT_LOW          0x34
#define EC_WATCHPT_CUR          0x35
#define EC_BKPT32               0x38
#define EC_VECTOR32             0x3a
#define EC_BRK64                0x3c

/* Exception context structure */
struct exception_context {
    uint64_t x0, x1, x2, x3, x4, x5, x6, x7;
    uint64_t x8, x9, x10, x11, x12, x13, x14, x15;
    uint64_t x16, x17, x18, x19, x20, x21, x22, x23;
    uint64_t x24, x25, x26, x27, x28, x29, x30;
    uint64_t sp, pc, pstate;
    uint64_t esr, far, elr;
};

static struct exception_context exception_ctx;

/*
 * Get exception class from ESR
 */
static uint32_t get_exception_class(uint64_t esr)
{
    return (esr >> ESR_EL1_EC_SHIFT) & ESR_EL1_EC_MASK;
}

/*
 * Get instruction specific syndrome from ESR
 */
static uint32_t get_iss(uint64_t esr)
{
    return esr & ESR_EL1_ISS_MASK;
}

/*
 * Print exception information
 */
static void print_exception_info(const char *type, uint32_t source, uint64_t esr, uint64_t far, uint64_t elr)
{
    uint32_t ec = get_exception_class(esr);
    uint32_t iss = get_iss(esr);
    
    printf("\n=== %s EXCEPTION ===\n", type);
    printf("Source: %u\n", source);
    printf("ESR_EL1: 0x%016lx\n", esr);
    printf("FAR_EL1: 0x%016lx\n", far);
    printf("ELR_EL1: 0x%016lx\n", elr);
    printf("Exception Class: 0x%02x", ec);
    
    switch (ec) {
    case EC_UNKNOWN:
        printf(" (Unknown)");
        break;
    case EC_WFI_WFE:
        printf(" (WFI/WFE)");
        break;
    case EC_SVC64:
        printf(" (SVC from AArch64)");
        break;
    case EC_HVC64:
        printf(" (HVC from AArch64)");
        break;
    case EC_SMC64:
        printf(" (SMC from AArch64)");
        break;
    case EC_SYS64:
        printf(" (System register access)");
        break;
    case EC_IABT_LOW:
        printf(" (Instruction abort from lower EL)");
        break;
    case EC_IABT_CUR:
        printf(" (Instruction abort from current EL)");
        break;
    case EC_PC_ALIGN:
        printf(" (PC alignment fault)");
        break;
    case EC_DABT_LOW:
        printf(" (Data abort from lower EL)");
        break;
    case EC_DABT_CUR:
        printf(" (Data abort from current EL)");
        break;
    case EC_SP_ALIGN:
        printf(" (SP alignment fault)");
        break;
    case EC_BREAKPT_CUR:
        printf(" (Breakpoint from current EL)");
        break;
    case EC_SOFTSTP_CUR:
        printf(" (Software step from current EL)");
        break;
    case EC_WATCHPT_CUR:
        printf(" (Watchpoint from current EL)");
        break;
    case EC_BRK64:
        printf(" (BRK instruction)");
        break;
    default:
        printf(" (Unknown)");
        break;
    }
    
    printf("\nISS: 0x%06x\n", iss);
}

/*
 * Print register dump
 */
static void print_register_dump(void)
{
    printf("\n=== REGISTER DUMP ===\n");
    printf("x0:  0x%016lx  x1:  0x%016lx\n", exception_ctx.x0, exception_ctx.x1);
    printf("x2:  0x%016lx  x3:  0x%016lx\n", exception_ctx.x2, exception_ctx.x3);
    printf("x4:  0x%016lx  x5:  0x%016lx\n", exception_ctx.x4, exception_ctx.x5);
    printf("x6:  0x%016lx  x7:  0x%016lx\n", exception_ctx.x6, exception_ctx.x7);
    printf("x8:  0x%016lx  x9:  0x%016lx\n", exception_ctx.x8, exception_ctx.x9);
    printf("x10: 0x%016lx  x11: 0x%016lx\n", exception_ctx.x10, exception_ctx.x11);
    printf("x12: 0x%016lx  x13: 0x%016lx\n", exception_ctx.x12, exception_ctx.x13);
    printf("x14: 0x%016lx  x15: 0x%016lx\n", exception_ctx.x14, exception_ctx.x15);
    printf("x16: 0x%016lx  x17: 0x%016lx\n", exception_ctx.x16, exception_ctx.x17);
    printf("x18: 0x%016lx  x19: 0x%016lx\n", exception_ctx.x18, exception_ctx.x19);
    printf("x20: 0x%016lx  x21: 0x%016lx\n", exception_ctx.x20, exception_ctx.x21);
    printf("x22: 0x%016lx  x23: 0x%016lx\n", exception_ctx.x22, exception_ctx.x23);
    printf("x24: 0x%016lx  x25: 0x%016lx\n", exception_ctx.x24, exception_ctx.x25);
    printf("x26: 0x%016lx  x27: 0x%016lx\n", exception_ctx.x26, exception_ctx.x27);
    printf("x28: 0x%016lx  x29: 0x%016lx\n", exception_ctx.x28, exception_ctx.x29);
    printf("x30: 0x%016lx  sp:  0x%016lx\n", exception_ctx.x30, exception_ctx.sp);
    printf("pc:  0x%016lx  pstate: 0x%016lx\n", exception_ctx.pc, exception_ctx.pstate);
}

/*
 * Handle synchronous exceptions
 */
void handle_sync_exception(uint32_t source)
{
    uint64_t esr, far, elr;
    
    __asm__ volatile("mrs %0, esr_el1" : "=r" (esr));
    __asm__ volatile("mrs %0, far_el1" : "=r" (far));
    __asm__ volatile("mrs %0, elr_el1" : "=r" (elr));
    
    exception_ctx.esr = esr;
    exception_ctx.far = far;
    exception_ctx.elr = elr;
    
    print_exception_info("SYNCHRONOUS", source, esr, far, elr);
    
    uint32_t ec = get_exception_class(esr);
    
    switch (ec) {
    case EC_SVC64:
        /* Handle system call */
        printf("System call not implemented\n");
        break;
        
    case EC_DABT_CUR:
    case EC_DABT_LOW:
        /* Handle data abort */
        printf("Data abort at address 0x%016lx\n", far);
        break;
        
    case EC_IABT_CUR:
    case EC_IABT_LOW:
        /* Handle instruction abort */
        printf("Instruction abort at address 0x%016lx\n", far);
        break;
        
    case EC_PC_ALIGN:
        /* Handle PC alignment fault */
        printf("PC alignment fault at 0x%016lx\n", elr);
        break;
        
    case EC_SP_ALIGN:
        /* Handle SP alignment fault */
        printf("SP alignment fault\n");
        break;
        
    case EC_BRK64:
        /* Handle breakpoint */
        printf("Breakpoint instruction\n");
        break;
        
    default:
        printf("Unhandled synchronous exception\n");
        break;
    }
    
    if (DEBUG_ENABLED) {
        print_register_dump();
    }
    
    /* Hang the system */
    printf("System halted due to exception\n");
    while (1) {
        __asm__ volatile("wfi");
    }
}

/*
 * Handle IRQ exceptions
 */
void handle_irq_exception(uint32_t source)
{
    printf("IRQ exception from source %u\n", source);
    
    /* TODO: Implement IRQ handling */
    
    /* For now, just acknowledge and return */
}

/*
 * Handle FIQ exceptions
 */
void handle_fiq_exception(uint32_t source)
{
    printf("FIQ exception from source %u\n", source);
    
    /* TODO: Implement FIQ handling */
    
    /* For now, just acknowledge and return */
}

/*
 * Handle SError exceptions
 */
void handle_serror_exception(uint32_t source)
{
    uint64_t esr, far, elr;
    
    __asm__ volatile("mrs %0, esr_el1" : "=r" (esr));
    __asm__ volatile("mrs %0, far_el1" : "=r" (far));
    __asm__ volatile("mrs %0, elr_el1" : "=r" (elr));
    
    print_exception_info("SERROR", source, esr, far, elr);
    
    if (DEBUG_ENABLED) {
        print_register_dump();
    }
    
    /* SError is usually fatal */
    printf("System halted due to SError\n");
    while (1) {
        __asm__ volatile("wfi");
    }
}

/*
 * Initialize exception handling
 */
void exceptions_init(void)
{
    /* Exception vectors are set up in start.S */
    printf("Exception handling initialized\n");
}

/*
 * Trigger a breakpoint for debugging
 */
void debug_breakpoint(void)
{
    __asm__ volatile("brk #0");
}
