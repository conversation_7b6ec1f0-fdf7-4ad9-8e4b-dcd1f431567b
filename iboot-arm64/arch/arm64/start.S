/*
 * iBooy Bootloader - ARM64 Entry Point
 *
 * This file contains the initial boot code that sets up the ARM64 processor
 * and transfers control to the C code.
 */

.section .text.boot, "ax"
.global _start

/*
 * Boot entry point
 * Called by QEMU or other bootloader
 */
_start:
    // Disable interrupts
    msr daifset, #0xf
    
    // Check current exception level
    mrs x0, CurrentEL
    lsr x0, x0, #2
    cmp x0, #3
    b.eq el3_entry
    cmp x0, #2
    b.eq el2_entry
    cmp x0, #1
    b.eq el1_entry
    
    // Should not reach here
    b hang

el3_entry:
    // Configure EL3 -> EL2 transition
    mov x0, #0x5b1          // RES1 bits
    msr scr_el3, x0
    
    mov x0, #0x3c9          // EL2h, disable all interrupts
    msr spsr_el3, x0
    
    adr x0, el2_entry
    msr elr_el3, x0
    eret

el2_entry:
    // Configure EL2 -> EL1 transition
    mov x0, #0x80000000     // HCR_EL2.RW = 1 (AArch64)
    msr hcr_el2, x0
    
    mov x0, #0x3c5          // EL1h, disable all interrupts
    msr spsr_el2, x0
    
    adr x0, el1_entry
    msr elr_el2, x0
    eret

el1_entry:
    // We're now in EL1

    // Set up stack pointer (use a fixed address for now)
    mov x0, #0x40100000
    mov sp, x0

    // Clear BSS section (use fixed addresses)
    mov x0, #0x40080000  // BSS start
    mov x1, #0x40090000  // BSS end
    sub x1, x1, x0
    bl memzero
    
    // Set up exception vectors
    ldr x0, =exception_vectors
    msr vbar_el1, x0
    
    // Enable floating point
    mov x0, #(3 << 20)      // FPEN = 11b
    msr cpacr_el1, x0
    isb
    
    // Initialize MMU
    bl mmu_init
    
    // Initialize CPU features
    bl cpu_init
    
    // Jump to C main function
    bl main
    
    // Should never return
    b hang

/*
 * Hang the system
 */
hang:
    wfi
    b hang

/*
 * Memory zero function
 * x0 = start address
 * x1 = size in bytes
 */
memzero:
    cbz x1, memzero_done
    str xzr, [x0], #8
    sub x1, x1, #8
    cbnz x1, memzero
memzero_done:
    ret

/*
 * Exception vectors table
 * Must be 2KB aligned
 */
.align 11
exception_vectors:
    // Current EL with SP0
    .align 7
    b sync_exception_sp0
    .align 7
    b irq_exception_sp0
    .align 7
    b fiq_exception_sp0
    .align 7
    b serror_exception_sp0
    
    // Current EL with SPx
    .align 7
    b sync_exception_spx
    .align 7
    b irq_exception_spx
    .align 7
    b fiq_exception_spx
    .align 7
    b serror_exception_spx
    
    // Lower EL using AArch64
    .align 7
    b sync_exception_lower_64
    .align 7
    b irq_exception_lower_64
    .align 7
    b fiq_exception_lower_64
    .align 7
    b serror_exception_lower_64
    
    // Lower EL using AArch32
    .align 7
    b sync_exception_lower_32
    .align 7
    b irq_exception_lower_32
    .align 7
    b fiq_exception_lower_32
    .align 7
    b serror_exception_lower_32

/*
 * Exception handlers - save context and call C handlers
 */
sync_exception_sp0:
    bl save_context
    mov x0, #0
    bl handle_sync_exception
    bl restore_context
    eret

irq_exception_sp0:
    bl save_context
    mov x0, #0
    bl handle_irq_exception
    bl restore_context
    eret

fiq_exception_sp0:
    bl save_context
    mov x0, #0
    bl handle_fiq_exception
    bl restore_context
    eret

serror_exception_sp0:
    bl save_context
    mov x0, #0
    bl handle_serror_exception
    bl restore_context
    eret

sync_exception_spx:
    bl save_context
    mov x0, #1
    bl handle_sync_exception
    bl restore_context
    eret

irq_exception_spx:
    bl save_context
    mov x0, #1
    bl handle_irq_exception
    bl restore_context
    eret

fiq_exception_spx:
    bl save_context
    mov x0, #1
    bl handle_fiq_exception
    bl restore_context
    eret

serror_exception_spx:
    bl save_context
    mov x0, #1
    bl handle_serror_exception
    bl restore_context
    eret

sync_exception_lower_64:
    bl save_context
    mov x0, #2
    bl handle_sync_exception
    bl restore_context
    eret

irq_exception_lower_64:
    bl save_context
    mov x0, #2
    bl handle_irq_exception
    bl restore_context
    eret

fiq_exception_lower_64:
    bl save_context
    mov x0, #2
    bl handle_fiq_exception
    bl restore_context
    eret

serror_exception_lower_64:
    bl save_context
    mov x0, #2
    bl handle_serror_exception
    bl restore_context
    eret

sync_exception_lower_32:
    bl save_context
    mov x0, #3
    bl handle_sync_exception
    bl restore_context
    eret

irq_exception_lower_32:
    bl save_context
    mov x0, #3
    bl handle_irq_exception
    bl restore_context
    eret

fiq_exception_lower_32:
    bl save_context
    mov x0, #3
    bl handle_fiq_exception
    bl restore_context
    eret

serror_exception_lower_32:
    bl save_context
    mov x0, #3
    bl handle_serror_exception
    bl restore_context
    eret

/*
 * Context save/restore
 */
save_context:
    // TODO: Implement context saving
    ret

restore_context:
    // TODO: Implement context restoration
    ret
