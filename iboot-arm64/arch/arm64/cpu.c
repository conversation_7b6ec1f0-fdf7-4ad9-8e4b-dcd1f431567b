/*
 * iBooy Bootloader - ARM64 CPU Initialization
 * 
 * This file implements CPU-specific initialization and feature detection.
 */

#include "../include/config.h"
#include <stdint.h>

/* CPU feature detection */
struct cpu_features {
    uint32_t midr;          /* Main ID Register */
    uint32_t revidr;        /* Revision ID Register */
    uint64_t id_aa64pfr0;   /* Processor Feature Register 0 */
    uint64_t id_aa64pfr1;   /* Processor Feature Register 1 */
    uint64_t id_aa64dfr0;   /* Debug Feature Register 0 */
    uint64_t id_aa64dfr1;   /* Debug Feature Register 1 */
    uint64_t id_aa64isar0;  /* Instruction Set Attribute Register 0 */
    uint64_t id_aa64isar1;  /* Instruction Set Attribute Register 1 */
    uint64_t id_aa64mmfr0;  /* Memory Model Feature Register 0 */
    uint64_t id_aa64mmfr1;  /* Memory Model Feature Register 1 */
    uint64_t ctr;           /* Cache Type Register */
    uint64_t dczid;         /* Data Cache Zero ID Register */
};

static struct cpu_features cpu_features;

/* CPU implementer IDs */
#define CPU_IMPL_ARM        0x41
#define CPU_IMPL_APPLE      0x61
#define CPU_IMPL_QEMU       0x51

/* ARM CPU part numbers */
#define CPU_PART_CORTEX_A53 0xd03
#define CPU_PART_CORTEX_A57 0xd07
#define CPU_PART_CORTEX_A72 0xd08
#define CPU_PART_CORTEX_A73 0xd09

/* Apple CPU part numbers */
#define CPU_PART_M1_FIRESTORM   0x021
#define CPU_PART_M1_ICESTORM    0x020

/*
 * Read CPU ID registers
 */
static void read_cpu_id_registers(void)
{
    __asm__ volatile("mrs %0, midr_el1" : "=r" (cpu_features.midr));
    __asm__ volatile("mrs %0, revidr_el1" : "=r" (cpu_features.revidr));
    __asm__ volatile("mrs %0, id_aa64pfr0_el1" : "=r" (cpu_features.id_aa64pfr0));
    __asm__ volatile("mrs %0, id_aa64pfr1_el1" : "=r" (cpu_features.id_aa64pfr1));
    __asm__ volatile("mrs %0, id_aa64dfr0_el1" : "=r" (cpu_features.id_aa64dfr0));
    __asm__ volatile("mrs %0, id_aa64dfr1_el1" : "=r" (cpu_features.id_aa64dfr1));
    __asm__ volatile("mrs %0, id_aa64isar0_el1" : "=r" (cpu_features.id_aa64isar0));
    __asm__ volatile("mrs %0, id_aa64isar1_el1" : "=r" (cpu_features.id_aa64isar1));
    __asm__ volatile("mrs %0, id_aa64mmfr0_el1" : "=r" (cpu_features.id_aa64mmfr0));
    __asm__ volatile("mrs %0, id_aa64mmfr1_el1" : "=r" (cpu_features.id_aa64mmfr1));
    __asm__ volatile("mrs %0, ctr_el0" : "=r" (cpu_features.ctr));
    __asm__ volatile("mrs %0, dczid_el0" : "=r" (cpu_features.dczid));
}

/*
 * Get CPU implementer
 */
static uint32_t get_cpu_implementer(void)
{
    return (cpu_features.midr >> 24) & 0xff;
}

/*
 * Get CPU part number
 */
static uint32_t get_cpu_part(void)
{
    return (cpu_features.midr >> 4) & 0xfff;
}

/*
 * Get CPU variant
 */
static uint32_t get_cpu_variant(void)
{
    return (cpu_features.midr >> 20) & 0xf;
}

/*
 * Get CPU revision
 */
static uint32_t get_cpu_revision(void)
{
    return cpu_features.midr & 0xf;
}

/*
 * Check if pointer authentication is supported
 */
static int has_pointer_auth(void)
{
    uint64_t apa = (cpu_features.id_aa64isar1 >> 4) & 0xf;
    uint64_t api = (cpu_features.id_aa64isar1 >> 8) & 0xf;
    return (apa != 0) || (api != 0);
}

/*
 * Check if SVE is supported
 */
static int has_sve(void)
{
    return (cpu_features.id_aa64pfr0 >> 32) & 0xf;
}

/*
 * Check if crypto extensions are supported
 */
static int has_crypto(void)
{
    uint64_t aes = (cpu_features.id_aa64isar0 >> 4) & 0xf;
    uint64_t sha1 = (cpu_features.id_aa64isar0 >> 8) & 0xf;
    uint64_t sha2 = (cpu_features.id_aa64isar0 >> 12) & 0xf;
    return (aes != 0) || (sha1 != 0) || (sha2 != 0);
}

/*
 * Enable pointer authentication
 */
static void enable_pointer_auth(void)
{
    if (!has_pointer_auth()) {
        return;
    }
    
    uint64_t sctlr;
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    sctlr |= (1UL << 30);  /* EnIA */
    sctlr |= (1UL << 27);  /* EnIB */
    sctlr |= (1UL << 13);  /* EnDA */
    sctlr |= (1UL << 12);  /* EnDB */
    __asm__ volatile("msr sctlr_el1, %0" : : "r" (sctlr));
    __asm__ volatile("isb");
}

/*
 * Initialize CPU caches
 */
static void init_caches(void)
{
    /* Enable instruction and data caches */
    uint64_t sctlr;
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    sctlr |= (1UL << 2);   /* C bit - data cache */
    sctlr |= (1UL << 12);  /* I bit - instruction cache */
    __asm__ volatile("msr sctlr_el1, %0" : : "r" (sctlr));
    __asm__ volatile("isb");
    
    /* Invalidate instruction cache */
    __asm__ volatile("ic iallu");
    __asm__ volatile("dsb ish");
    __asm__ volatile("isb");
}

/*
 * Print CPU information
 */
void cpu_print_info(void)
{
    uint32_t implementer = get_cpu_implementer();
    uint32_t part = get_cpu_part();
    uint32_t variant = get_cpu_variant();
    uint32_t revision = get_cpu_revision();
    
    printf("CPU: ");
    
    switch (implementer) {
    case CPU_IMPL_ARM:
        printf("ARM ");
        switch (part) {
        case CPU_PART_CORTEX_A53:
            printf("Cortex-A53");
            break;
        case CPU_PART_CORTEX_A57:
            printf("Cortex-A57");
            break;
        case CPU_PART_CORTEX_A72:
            printf("Cortex-A72");
            break;
        case CPU_PART_CORTEX_A73:
            printf("Cortex-A73");
            break;
        default:
            printf("Unknown (0x%03x)", part);
            break;
        }
        break;
        
    case CPU_IMPL_APPLE:
        printf("Apple ");
        switch (part) {
        case CPU_PART_M1_FIRESTORM:
            printf("M1 Firestorm");
            break;
        case CPU_PART_M1_ICESTORM:
            printf("M1 Icestorm");
            break;
        default:
            printf("Unknown (0x%03x)", part);
            break;
        }
        break;
        
    case CPU_IMPL_QEMU:
        printf("QEMU Virtual CPU");
        break;
        
    default:
        printf("Unknown implementer 0x%02x part 0x%03x", implementer, part);
        break;
    }
    
    printf(" r%up%u\n", variant, revision);
    
    /* Print features */
    printf("Features:");
    if (has_pointer_auth()) {
        printf(" PAC");
    }
    if (has_sve()) {
        printf(" SVE");
    }
    if (has_crypto()) {
        printf(" CRYPTO");
    }
    printf("\n");
}

/*
 * Get current exception level
 */
uint32_t cpu_get_current_el(void)
{
    uint64_t current_el;
    __asm__ volatile("mrs %0, CurrentEL" : "=r" (current_el));
    return (current_el >> 2) & 3;
}

/*
 * Initialize CPU
 */
void cpu_init(void)
{
    /* Read CPU identification registers */
    read_cpu_id_registers();
    
    /* Initialize caches */
    init_caches();
    
    /* Enable pointer authentication if supported */
    if (PAC_ENABLED && has_pointer_auth()) {
        enable_pointer_auth();
    }
    
    /* Print CPU information */
    if (DEBUG_ENABLED) {
        cpu_print_info();
        printf("Current EL: %u\n", cpu_get_current_el());
    }
}

/*
 * Get CPU features structure
 */
const struct cpu_features *cpu_get_features(void)
{
    return &cpu_features;
}

/*
 * CPU-specific delay
 */
void cpu_delay(uint32_t cycles)
{
    for (volatile uint32_t i = 0; i < cycles; i++) {
        __asm__ volatile("nop");
    }
}

/*
 * Wait for interrupt
 */
void cpu_wfi(void)
{
    __asm__ volatile("wfi");
}

/*
 * Wait for event
 */
void cpu_wfe(void)
{
    __asm__ volatile("wfe");
}

/*
 * Send event
 */
void cpu_sev(void)
{
    __asm__ volatile("sev");
}

/*
 * Data synchronization barrier
 */
void cpu_dsb(void)
{
    __asm__ volatile("dsb sy");
}

/*
 * Data memory barrier
 */
void cpu_dmb(void)
{
    __asm__ volatile("dmb sy");
}

/*
 * Instruction synchronization barrier
 */
void cpu_isb(void)
{
    __asm__ volatile("isb");
}
