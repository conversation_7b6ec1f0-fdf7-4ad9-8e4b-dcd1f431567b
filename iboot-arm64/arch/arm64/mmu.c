/*
 * iBooy Bootloader - ARM64 MMU Implementation
 * 
 * This file implements the Memory Management Unit (MMU) setup for ARM64.
 * It configures page tables and enables virtual memory.
 */

#include "../include/config.h"
#include <stdint.h>

/* MMU Control Register bits */
#define SCTLR_EL1_M     (1 << 0)    /* MMU enable */
#define SCTLR_EL1_A     (1 << 1)    /* Alignment check enable */
#define SCTLR_EL1_C     (1 << 2)    /* Data cache enable */
#define SCTLR_EL1_SA    (1 << 3)    /* Stack alignment check */
#define SCTLR_EL1_I     (1 << 12)   /* Instruction cache enable */

/* Translation Control Register bits */
#define TCR_EL1_T0SZ(x) ((x) & 0x3f)
#define TCR_EL1_IRGN0_NC    (0 << 8)
#define TCR_EL1_IRGN0_WBWA  (1 << 8)
#define TCR_EL1_IRGN0_WT    (2 << 8)
#define TCR_EL1_IRGN0_WB    (3 << 8)
#define TCR_EL1_ORGN0_NC    (0 << 10)
#define TCR_EL1_ORGN0_WBWA  (1 << 10)
#define TCR_EL1_ORGN0_WT    (2 << 10)
#define TCR_EL1_ORGN0_WB    (3 << 10)
#define TCR_EL1_SH0_NS      (0 << 12)
#define TCR_EL1_SH0_OS      (2 << 12)
#define TCR_EL1_SH0_IS      (3 << 12)
#define TCR_EL1_TG0_4K      (0 << 14)
#define TCR_EL1_TG0_64K     (1 << 14)
#define TCR_EL1_TG0_16K     (2 << 14)

/* Memory Attribute Indirection Register */
#define MAIR_DEVICE_nGnRnE  0x00
#define MAIR_DEVICE_nGnRE   0x04
#define MAIR_DEVICE_GRE     0x0c
#define MAIR_NORMAL_NC      0x44
#define MAIR_NORMAL_WT      0xbb
#define MAIR_NORMAL_WB      0xff

/* Page table entry bits */
#define PTE_VALID           (1UL << 0)
#define PTE_TABLE           (1UL << 1)
#define PTE_BLOCK           (0UL << 1)
#define PTE_PAGE            (1UL << 1)
#define PTE_USER            (1UL << 6)
#define PTE_RDONLY          (1UL << 7)
#define PTE_SHARED          (3UL << 8)
#define PTE_AF              (1UL << 10)
#define PTE_NG              (1UL << 11)
#define PTE_PXN             (1UL << 53)
#define PTE_UXN             (1UL << 54)

/* Memory types */
#define MT_DEVICE_nGnRnE    0
#define MT_DEVICE_nGnRE     1
#define MT_DEVICE_GRE       2
#define MT_NORMAL_NC        3
#define MT_NORMAL           4

/* Page table levels */
#define LEVEL0_SHIFT        39
#define LEVEL1_SHIFT        30
#define LEVEL2_SHIFT        21
#define LEVEL3_SHIFT        12

#define LEVEL_MASK          0x1ff
#define BLOCK_SIZE_L1       (1UL << LEVEL1_SHIFT)  /* 1GB */
#define BLOCK_SIZE_L2       (1UL << LEVEL2_SHIFT)  /* 2MB */
#define PAGE_SIZE           (1UL << LEVEL3_SHIFT)  /* 4KB */

/* Page table storage */
static uint64_t page_table_l0[512] __attribute__((aligned(4096)));
static uint64_t page_table_l1[512] __attribute__((aligned(4096)));
static uint64_t page_table_l2[512] __attribute__((aligned(4096)));

/*
 * Create a page table entry
 */
static uint64_t create_pte(uint64_t pa, uint64_t attr, uint64_t type)
{
    return (pa & ~0xfff) | attr | type | PTE_VALID | PTE_AF;
}

/*
 * Map a memory region
 */
static void map_region(uint64_t va, uint64_t pa, uint64_t size, uint64_t attr)
{
    uint64_t l0_idx, l1_idx, l2_idx;
    uint64_t addr = va;
    uint64_t phys = pa;
    uint64_t remaining = size;
    
    while (remaining > 0) {
        l0_idx = (addr >> LEVEL0_SHIFT) & LEVEL_MASK;
        l1_idx = (addr >> LEVEL1_SHIFT) & LEVEL_MASK;
        l2_idx = (addr >> LEVEL2_SHIFT) & LEVEL_MASK;
        
        /* Set up L0 entry if needed */
        if (!(page_table_l0[l0_idx] & PTE_VALID)) {
            page_table_l0[l0_idx] = create_pte((uint64_t)page_table_l1, 0, PTE_TABLE);
        }
        
        /* Set up L1 entry */
        if (remaining >= BLOCK_SIZE_L1 && (addr & (BLOCK_SIZE_L1 - 1)) == 0) {
            /* Use 1GB block */
            page_table_l1[l1_idx] = create_pte(phys, attr, PTE_BLOCK);
            addr += BLOCK_SIZE_L1;
            phys += BLOCK_SIZE_L1;
            remaining -= BLOCK_SIZE_L1;
        } else {
            /* Need L2 table */
            if (!(page_table_l1[l1_idx] & PTE_VALID)) {
                page_table_l1[l1_idx] = create_pte((uint64_t)page_table_l2, 0, PTE_TABLE);
            }
            
            /* Use 2MB block */
            if (remaining >= BLOCK_SIZE_L2 && (addr & (BLOCK_SIZE_L2 - 1)) == 0) {
                page_table_l2[l2_idx] = create_pte(phys, attr, PTE_BLOCK);
                addr += BLOCK_SIZE_L2;
                phys += BLOCK_SIZE_L2;
                remaining -= BLOCK_SIZE_L2;
            } else {
                /* Would need L3 table for 4KB pages - not implemented */
                addr += PAGE_SIZE;
                phys += PAGE_SIZE;
                remaining -= PAGE_SIZE;
            }
        }
    }
}

/*
 * Initialize the MMU
 */
void mmu_init(void)
{
    uint64_t mair, tcr, sctlr;
    
    /* Clear page tables */
    for (int i = 0; i < 512; i++) {
        page_table_l0[i] = 0;
        page_table_l1[i] = 0;
        page_table_l2[i] = 0;
    }
    
    /* Set up memory attribute indirection register */
    mair = ((uint64_t)MAIR_DEVICE_nGnRnE << (8 * MT_DEVICE_nGnRnE)) |
           ((uint64_t)MAIR_DEVICE_nGnRE << (8 * MT_DEVICE_nGnRE)) |
           ((uint64_t)MAIR_DEVICE_GRE << (8 * MT_DEVICE_GRE)) |
           ((uint64_t)MAIR_NORMAL_NC << (8 * MT_NORMAL_NC)) |
           ((uint64_t)MAIR_NORMAL_WB << (8 * MT_NORMAL));
    
    __asm__ volatile("msr mair_el1, %0" : : "r" (mair));
    
    /* Set up translation control register */
    tcr = TCR_EL1_T0SZ(25) |        /* 39-bit virtual addresses */
          TCR_EL1_IRGN0_WBWA |      /* Inner write-back write-allocate */
          TCR_EL1_ORGN0_WBWA |      /* Outer write-back write-allocate */
          TCR_EL1_SH0_IS |          /* Inner shareable */
          TCR_EL1_TG0_4K;           /* 4KB granule */
    
    __asm__ volatile("msr tcr_el1, %0" : : "r" (tcr));
    
    /* Map memory regions */
    
    /* Identity map RAM (normal memory) */
    map_region(MEMORY_BASE, MEMORY_BASE, MEMORY_SIZE, 
               (MT_NORMAL << 2) | PTE_SHARED);
    
    /* Map UART (device memory) */
    map_region(UART_BASE, UART_BASE, 0x1000,
               (MT_DEVICE_nGnRE << 2));
    
    /* Map other QEMU virt peripherals */
    map_region(0x08000000, 0x08000000, 0x02000000,  /* GIC, etc. */
               (MT_DEVICE_nGnRE << 2));
    
    /* Set translation table base register */
    __asm__ volatile("msr ttbr0_el1, %0" : : "r" ((uint64_t)page_table_l0));
    
    /* Invalidate TLB */
    __asm__ volatile("tlbi vmalle1is");
    __asm__ volatile("dsb ish");
    __asm__ volatile("isb");
    
    /* Enable MMU */
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    sctlr |= SCTLR_EL1_M | SCTLR_EL1_C | SCTLR_EL1_I;
    __asm__ volatile("msr sctlr_el1, %0" : : "r" (sctlr));
    __asm__ volatile("isb");
}

/*
 * Disable MMU
 */
void mmu_disable(void)
{
    uint64_t sctlr;
    
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    sctlr &= ~(SCTLR_EL1_M | SCTLR_EL1_C | SCTLR_EL1_I);
    __asm__ volatile("msr sctlr_el1, %0" : : "r" (sctlr));
    __asm__ volatile("isb");
}

/*
 * Check if MMU is enabled
 */
int mmu_is_enabled(void)
{
    uint64_t sctlr;
    __asm__ volatile("mrs %0, sctlr_el1" : "=r" (sctlr));
    return (sctlr & SCTLR_EL1_M) != 0;
}
