#ifndef CONFIG_H
#define CONFIG_H

/* iBooy Bootloader Configuration */

/* Version Information */
#define IBOOY_VERSION_MAJOR     1
#define IBOOY_VERSION_MINOR     0
#define IBOOY_VERSION_PATCH     0
#define IBOOY_VERSION_STRING    "1.0.0"

/* Memory Layout */
#define MEMORY_BASE             0x40000000UL
#define MEMORY_SIZE             0x08000000UL    /* 128MB */
#define STACK_SIZE              0x00010000UL    /* 64KB */
#define HEAP_SIZE               0x00100000UL    /* 1MB */

/* Boot Configuration */
#define MAX_BOOT_ARGS_SIZE      1024
#define MAX_KERNEL_SIZE         0x02000000UL    /* 32MB */
#define KERNEL_LOAD_ADDR        0x41000000UL
#define DTB_LOAD_ADDR           0x43000000UL
#define RAMDISK_LOAD_ADDR       0x44000000UL

/* Platform Support */
#define PLATFORM_QEMU_ARM64     1
#define PLATFORM_M1_FAKE        2
#define PLATFORM_RPI4           3

#ifndef TARGET_PLATFORM
#define TARGET_PLATFORM         PLATFORM_QEMU_ARM64
#endif

/* UART Configuration */
#define UART_BASE               0x09000000UL
#define UART_BAUDRATE           115200
#define UART_CLOCK              24000000

/* Timer Configuration */
#define TIMER_FREQUENCY         62500000    /* 62.5MHz */

/* Filesystem Support */
#define FS_SUPPORT_FAT          1
#define FS_SUPPORT_APFS         1
#define FS_SUPPORT_IMG4         1

/* Crypto Support */
#define CRYPTO_SUPPORT_AES      1
#define CRYPTO_SUPPORT_SHA      1
#define CRYPTO_SUPPORT_RSA      1

/* Security Features */
#define SECURE_BOOT_ENABLED     1
#define PAC_ENABLED             1
#define POINTER_AUTH_ENABLED    1

/* Debug Configuration */
#define DEBUG_ENABLED           1
#define VERBOSE_BOOT            1
#define SHELL_ENABLED           1

/* MMU Configuration */
#define MMU_GRANULE_SIZE        4096        /* 4KB pages */
#define MMU_VA_BITS             39          /* 39-bit virtual addresses */
#define MMU_PA_BITS             40          /* 40-bit physical addresses */

/* Exception Levels */
#define CURRENT_EL              1           /* EL1 */
#define TARGET_EL               1           /* Target EL1 */

/* Boot Sources */
#define BOOT_SOURCE_EMMC        1
#define BOOT_SOURCE_USB         2
#define BOOT_SOURCE_NETWORK     3
#define BOOT_SOURCE_TFTP        4

/* Default Boot Source */
#define DEFAULT_BOOT_SOURCE     BOOT_SOURCE_EMMC

/* Compression Support */
#define COMPRESSION_LZSS        1
#define COMPRESSION_GZIP        1

/* GUI Configuration */
#define GUI_ENABLED             1
#define GUI_WIDTH               1024
#define GUI_HEIGHT              768
#define GUI_BPP                 32

/* Shell Configuration */
#define SHELL_PROMPT            "iBooy> "
#define SHELL_MAX_CMDLINE       256
#define SHELL_HISTORY_SIZE      10

/* Device Tree Configuration */
#define DTB_MAX_SIZE            0x00010000UL    /* 64KB */
#define FDT_MAGIC               0xd00dfeed

/* IMG4 Configuration */
#define IMG4_MAGIC              0x494D4734      /* 'IMG4' */
#define IMG4_MAX_SIZE           0x01000000UL    /* 16MB */

/* Error Codes */
#define IBOOY_SUCCESS           0
#define IBOOY_ERROR_GENERIC     -1
#define IBOOY_ERROR_NOMEM       -2
#define IBOOY_ERROR_INVALID     -3
#define IBOOY_ERROR_NOTFOUND    -4
#define IBOOY_ERROR_IO          -5
#define IBOOY_ERROR_CRYPTO      -6
#define IBOOY_ERROR_AUTH        -7

/* Utility Macros */
#define ALIGN_UP(x, a)          (((x) + (a) - 1) & ~((a) - 1))
#define ALIGN_DOWN(x, a)        ((x) & ~((a) - 1))
#define ARRAY_SIZE(x)           (sizeof(x) / sizeof((x)[0]))
#define MIN(a, b)               ((a) < (b) ? (a) : (b))
#define MAX(a, b)               ((a) > (b) ? (a) : (b))

/* Register Access Macros */
#define REG32(addr)             (*(volatile uint32_t *)(addr))
#define REG64(addr)             (*(volatile uint64_t *)(addr))

/* Bit Manipulation */
#define BIT(n)                  (1UL << (n))
#define BITS(h, l)              ((~0UL << (l)) & (~0UL >> (63 - (h))))
#define FIELD_GET(mask, val)    (((val) & (mask)) >> __builtin_ctzl(mask))
#define FIELD_PREP(mask, val)   (((val) << __builtin_ctzl(mask)) & (mask))

#endif /* CONFIG_H */
